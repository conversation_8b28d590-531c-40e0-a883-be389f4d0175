# Advanced Quote Generator

A high-performance, feature-rich quote generator application built with Python. This application efficiently handles large quote datasets (500K+ quotes) with advanced features including categorization, search, rating, and favorites management.

## Features

### Core Functionality
- **Random Quote Generation**: Intelligent weighted selection based on ratings and favorites
- **Advanced Search**: Full-text search across quotes, authors, and categories
- **Smart Filtering**: Filter by author, category, rating, and favorites
- **User Interactions**: Rate quotes (1-5 stars) and mark favorites

### Performance Optimizations
- **Memory Efficient**: Chunked processing for large CSV files
- **Fast Indexing**: Pre-built indexes for authors, categories, and ratings
- **Intelligent Caching**: LRU cache for search results and processed data
- **Lazy Loading**: Data loaded on-demand to minimize memory usage

### Advanced Features
- **Statistics Dashboard**: Comprehensive analytics about your quote collection
- **Export Functionality**: Export quotes in multiple formats (JSON, CSV, TXT)
- **View Tracking**: Track quote popularity and viewing history
- **Colored CLI**: Beautiful, colored command-line interface

## Installation

1. **Clone or download the repository**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Ensure your quotes CSV file is in the root directory** (named `quotes.csv`)

## Quick Start

### Basic Usage

```bash
# Get a random quote
python src/main.py --random

# Search for quotes about love
python src/main.py --search "love"

# Get quotes by a specific author
python src/main.py --author "Einstein"

# Filter by category
python src/main.py --category "inspirational"

# Show collection statistics
python src/main.py --stats
```

### Advanced Usage

```bash
# Limit results and use compact format
python src/main.py --search "wisdom" --limit 5 --compact

# Disable colored output
python src/main.py --random --no-color

# Debug mode with memory monitoring
python src/main.py --stats --debug --memory

# Use custom CSV file
python src/main.py --csv /path/to/your/quotes.csv --random
```

## CSV File Format

Your quotes CSV file should have the following structure:

```csv
quote,author,category
"Quote text here","Author Name","category1, category2, category3"
"Another quote","Another Author","single-category"
```

**Required columns:**
- `quote`: The quote text
- `author`: Author name
- `category`: Comma-separated list of categories/tags

## Project Structure

```
q-generator/
├── src/
│   ├── core/                    # Core business logic
│   │   ├── quote_manager.py     # Main quote management
│   │   └── data_loader.py       # Efficient CSV loading
│   ├── data/
│   │   └── models.py            # Data models (Quote, Author, Category)
│   ├── utils/
│   │   ├── performance.py       # Performance utilities
│   │   └── formatters.py        # Output formatting
│   ├── cli/                     # Command-line interface (future)
│   ├── tests/                   # Unit tests
│   └── main.py                  # Application entry point
├── config/
│   ├── settings.py              # Configuration settings
│   └── logging_config.py        # Logging setup
├── data/
│   ├── cache/                   # Cached processed data
│   └── user_data/               # User preferences and ratings
├── requirements.txt             # Python dependencies
├── quotes.csv                   # Your quotes dataset
└── README.md                    # This file
```

## Configuration

The application can be configured through environment variables:

- `QUOTES_CSV_PATH`: Path to your quotes CSV file
- `QUOTE_GENERATOR_DEV`: Set to "true" for development mode

## Performance

The application is optimized for large datasets:

- **Memory Usage**: ~50-100MB for 500K quotes
- **Load Time**: ~10-30 seconds for initial processing (cached afterwards)
- **Search Speed**: Sub-second search across entire dataset
- **Filtering**: Near-instantaneous with pre-built indexes

## Testing

Run the test suite:

```bash
python -m pytest src/tests/ -v
```

Or run individual test files:

```bash
python src/tests/test_quote_manager.py
```

## Development

### Adding New Features

1. **Data Models**: Extend models in `src/data/models.py`
2. **Core Logic**: Add functionality to `src/core/quote_manager.py`
3. **CLI Commands**: Add commands to `src/main.py`
4. **Tests**: Add tests in `src/tests/`

### Performance Monitoring

Enable debug mode to see performance metrics:

```bash
python src/main.py --stats --debug --memory
```

## Troubleshooting

### Common Issues

1. **"File not found" error**: Ensure `quotes.csv` is in the root directory
2. **Memory issues**: Reduce chunk size in settings for lower memory usage
3. **Slow loading**: Enable caching (default) for faster subsequent loads
4. **Import errors**: Ensure all dependencies are installed

### Debug Mode

Use debug mode for detailed logging:

```bash
python src/main.py --debug --memory
```

## Contributing

1. Follow the modular coding structure
2. Add comprehensive tests for new features
3. Use proper docstrings and comments
4. Ensure performance optimizations are maintained

## License

This project is open source and available under the MIT License.

## Future Enhancements

- Interactive CLI with menu system
- Web interface
- Quote recommendation engine
- Social sharing features
- Import from multiple sources
- Advanced analytics and insights
