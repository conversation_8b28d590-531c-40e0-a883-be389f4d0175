"""
Configuration settings for the quote generator application.

This module contains all configurable parameters and settings used
throughout the application.
"""

import os
from pathlib import Path


class Settings:
    """
    Application settings and configuration.
    """
    
    # File paths
    BASE_DIR = Path(__file__).parent.parent
    DATA_DIR = BASE_DIR / "data"
    USER_DATA_DIR = DATA_DIR / "user_data"
    CACHE_DIR = DATA_DIR / "cache"
    
    # Default CSV file path
    DEFAULT_CSV_PATH = BASE_DIR / "quotes.csv"
    
    # Database settings
    USER_DB_PATH = USER_DATA_DIR / "user_data.db"
    
    # Performance settings
    DEFAULT_CHUNK_SIZE = 10000
    MAX_CACHE_SIZE = 1000
    MAX_SEARCH_CACHE_SIZE = 100
    MAX_VIEW_HISTORY = 1000
    
    # Display settings
    DEFAULT_TERMINAL_WIDTH = 80
    MAX_QUOTES_PER_PAGE = 20
    COMPACT_QUOTE_LENGTH = 60
    
    # Random selection settings
    ENABLE_WEIGHTED_SELECTION = True
    FAVORITE_WEIGHT_MULTIPLIER = 1.5
    HIGH_RATING_WEIGHT_MULTIPLIER = 1.3
    HIGH_VIEW_COUNT_WEIGHT_REDUCER = 0.9
    HIGH_VIEW_COUNT_THRESHOLD = 10
    
    # Rating settings
    MIN_RATING = 0.0
    MAX_RATING = 5.0
    DEFAULT_RATING_PRECISION = 1
    
    # Search settings
    SEARCH_RESULT_LIMIT = 50
    ENABLE_SEARCH_CACHING = True
    
    # Export settings
    EXPORT_DIR = DATA_DIR / "exports"
    SUPPORTED_EXPORT_FORMATS = ["json", "csv", "txt"]
    
    # Logging settings
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE = BASE_DIR / "logs" / "quote_generator.log"
    
    # CLI settings
    ENABLE_COLORED_OUTPUT = True
    ENABLE_PROGRESS_BARS = True
    
    @classmethod
    def create_directories(cls):
        """Create necessary directories if they don't exist."""
        directories = [
            cls.DATA_DIR,
            cls.USER_DATA_DIR,
            cls.CACHE_DIR,
            cls.EXPORT_DIR,
            cls.LOG_FILE.parent
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_csv_path(cls) -> Path:
        """
        Get the path to the quotes CSV file.
        
        Returns:
            Path to CSV file
        """
        # Check environment variable first
        env_path = os.getenv("QUOTES_CSV_PATH")
        if env_path:
            return Path(env_path)
        
        # Use default path
        return cls.DEFAULT_CSV_PATH
    
    @classmethod
    def is_development_mode(cls) -> bool:
        """
        Check if application is running in development mode.
        
        Returns:
            True if in development mode
        """
        return os.getenv("QUOTE_GENERATOR_DEV", "false").lower() == "true"


# Environment-specific settings
class DevelopmentSettings(Settings):
    """Settings for development environment."""
    LOG_LEVEL = "DEBUG"
    DEFAULT_CHUNK_SIZE = 1000  # Smaller chunks for faster testing
    ENABLE_PROGRESS_BARS = False  # Less noise during development


class ProductionSettings(Settings):
    """Settings for production environment."""
    LOG_LEVEL = "WARNING"
    DEFAULT_CHUNK_SIZE = 20000  # Larger chunks for better performance
    ENABLE_PROGRESS_BARS = True


def get_settings() -> Settings:
    """
    Get appropriate settings based on environment.
    
    Returns:
        Settings instance
    """
    if Settings.is_development_mode():
        return DevelopmentSettings()
    else:
        return ProductionSettings()


# Global settings instance
settings = get_settings()
