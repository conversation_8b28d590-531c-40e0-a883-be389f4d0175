#!/usr/bin/env python3
"""
Benchmark script for the Quote Generator application.

This script measures performance of various operations to ensure
the application meets performance requirements.
"""

import sys
import time
import statistics
from pathlib import Path

# Add src to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from config.settings import settings
from core.quote_manager import QuoteManager
from utils.performance import get_memory_usage


def benchmark_loading(csv_path: str, iterations: int = 3):
    """Benchmark quote loading performance."""
    print("🔄 Benchmarking quote loading...")
    
    load_times = []
    
    for i in range(iterations):
        print(f"  Iteration {i+1}/{iterations}")
        
        # Clear cache for fair testing
        quote_manager = QuoteManager(csv_path)
        quote_manager.data_loader.clear_cache()
        
        start_time = time.perf_counter()
        quote_count = quote_manager.load_quotes(use_cache=False)
        end_time = time.perf_counter()
        
        load_time = end_time - start_time
        load_times.append(load_time)
        
        print(f"    Loaded {quote_count} quotes in {load_time:.2f} seconds")
    
    avg_time = statistics.mean(load_times)
    min_time = min(load_times)
    max_time = max(load_times)
    
    print(f"  Average: {avg_time:.2f}s, Min: {min_time:.2f}s, Max: {max_time:.2f}s")
    return avg_time


def benchmark_search(quote_manager: QuoteManager, iterations: int = 10):
    """Benchmark search performance."""
    print("🔍 Benchmarking search performance...")
    
    search_terms = ["love", "life", "wisdom", "happiness", "success", 
                   "Einstein", "Shakespeare", "inspirational", "truth", "peace"]
    
    search_times = []
    
    for term in search_terms[:iterations]:
        start_time = time.perf_counter()
        results = quote_manager.search_quotes(term, limit=100)
        end_time = time.perf_counter()
        
        search_time = end_time - start_time
        search_times.append(search_time)
        
        print(f"  '{term}': {len(results)} results in {search_time:.4f}s")
    
    avg_time = statistics.mean(search_times)
    print(f"  Average search time: {avg_time:.4f}s")
    return avg_time


def benchmark_filtering(quote_manager: QuoteManager):
    """Benchmark filtering performance."""
    print("🔽 Benchmarking filtering performance...")
    
    # Author filtering
    start_time = time.perf_counter()
    author_results = quote_manager.filter_by_author("Einstein")
    author_time = time.perf_counter() - start_time
    print(f"  Author filter: {len(author_results)} results in {author_time:.4f}s")
    
    # Category filtering
    start_time = time.perf_counter()
    category_results = quote_manager.filter_by_category("love")
    category_time = time.perf_counter() - start_time
    print(f"  Category filter: {len(category_results)} results in {category_time:.4f}s")
    
    # Rating filtering
    start_time = time.perf_counter()
    rating_results = quote_manager.filter_by_rating(4.0, 5.0)
    rating_time = time.perf_counter() - start_time
    print(f"  Rating filter: {len(rating_results)} results in {rating_time:.4f}s")
    
    return (author_time + category_time + rating_time) / 3


def benchmark_random_generation(quote_manager: QuoteManager, iterations: int = 1000):
    """Benchmark random quote generation."""
    print("🎲 Benchmarking random quote generation...")
    
    start_time = time.perf_counter()
    
    for _ in range(iterations):
        quote = quote_manager.get_random_quote()
    
    end_time = time.perf_counter()
    total_time = end_time - start_time
    avg_time = total_time / iterations
    
    print(f"  Generated {iterations} random quotes in {total_time:.4f}s")
    print(f"  Average time per quote: {avg_time:.6f}s")
    return avg_time


def benchmark_statistics(quote_manager: QuoteManager):
    """Benchmark statistics generation."""
    print("📊 Benchmarking statistics generation...")
    
    start_time = time.perf_counter()
    stats = quote_manager.get_statistics()
    end_time = time.perf_counter()
    
    stats_time = end_time - start_time
    print(f"  Generated statistics in {stats_time:.4f}s")
    print(f"  Total quotes: {stats['total_quotes']}")
    print(f"  Unique authors: {stats['unique_authors']}")
    print(f"  Unique categories: {stats['unique_categories']}")
    
    return stats_time


def measure_memory_usage(quote_manager: QuoteManager):
    """Measure memory usage."""
    print("💾 Measuring memory usage...")
    
    memory_info = get_memory_usage()
    
    print(f"  RSS Memory: {memory_info['rss']:.1f} MB")
    print(f"  VMS Memory: {memory_info['vms']:.1f} MB")
    print(f"  Memory %: {memory_info['percent']:.1f}%")
    print(f"  Available: {memory_info['available']:.1f} MB")
    
    return memory_info


def main():
    """Main benchmark function."""
    print("🏁 Quote Generator Performance Benchmark")
    print("=" * 50)
    
    csv_path = str(settings.get_csv_path())
    
    if not Path(csv_path).exists():
        print(f"❌ CSV file not found: {csv_path}")
        sys.exit(1)
    
    # Initial memory measurement
    print("📏 Initial memory usage:")
    initial_memory = get_memory_usage()
    print(f"  RSS: {initial_memory['rss']:.1f} MB")
    
    print()
    
    # Benchmark loading
    avg_load_time = benchmark_loading(csv_path, iterations=1)  # Reduced for large dataset
    
    print()
    
    # Create quote manager for other benchmarks
    print("🔄 Loading quotes for benchmarks...")
    quote_manager = QuoteManager(csv_path)
    quote_count = quote_manager.load_quotes()
    
    print()
    
    # Memory after loading
    memory_after_load = measure_memory_usage(quote_manager)
    
    print()
    
    # Benchmark search
    avg_search_time = benchmark_search(quote_manager, iterations=5)
    
    print()
    
    # Benchmark filtering
    avg_filter_time = benchmark_filtering(quote_manager)
    
    print()
    
    # Benchmark random generation
    avg_random_time = benchmark_random_generation(quote_manager, iterations=100)
    
    print()
    
    # Benchmark statistics
    stats_time = benchmark_statistics(quote_manager)
    
    print()
    print("=" * 50)
    print("📋 BENCHMARK SUMMARY")
    print("=" * 50)
    print(f"Dataset size: {quote_count:,} quotes")
    print(f"Memory usage: {memory_after_load['rss']:.1f} MB")
    print(f"Load time: {avg_load_time:.2f} seconds")
    print(f"Search time: {avg_search_time:.4f} seconds (avg)")
    print(f"Filter time: {avg_filter_time:.4f} seconds (avg)")
    print(f"Random quote: {avg_random_time:.6f} seconds (avg)")
    print(f"Statistics: {stats_time:.4f} seconds")
    
    # Performance ratings
    print()
    print("🏆 PERFORMANCE RATINGS")
    print("-" * 25)
    
    # Rate loading performance (target: < 60s for 500K quotes)
    load_rating = "🟢 Excellent" if avg_load_time < 30 else "🟡 Good" if avg_load_time < 60 else "🔴 Needs improvement"
    print(f"Loading: {load_rating}")
    
    # Rate search performance (target: < 0.1s)
    search_rating = "🟢 Excellent" if avg_search_time < 0.05 else "🟡 Good" if avg_search_time < 0.1 else "🔴 Needs improvement"
    print(f"Search: {search_rating}")
    
    # Rate memory usage (target: < 200MB for 500K quotes)
    memory_rating = "🟢 Excellent" if memory_after_load['rss'] < 100 else "🟡 Good" if memory_after_load['rss'] < 200 else "🔴 Needs improvement"
    print(f"Memory: {memory_rating}")
    
    # Rate random generation (target: < 0.001s)
    random_rating = "🟢 Excellent" if avg_random_time < 0.0005 else "🟡 Good" if avg_random_time < 0.001 else "🔴 Needs improvement"
    print(f"Random: {random_rating}")


if __name__ == "__main__":
    main()
