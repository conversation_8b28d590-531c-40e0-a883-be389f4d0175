#!/usr/bin/env python3
"""
Setup script for the Quote Generator application.

This script helps with initial setup and configuration of the application.
"""

import os
import sys
import subprocess
from pathlib import Path


def install_dependencies():
    """Install required Python dependencies."""
    print("Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    print("Creating directories...")
    
    directories = [
        "data/cache",
        "data/user_data",
        "data/exports",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"  Created: {directory}")
    
    print("✅ Directories created successfully!")


def check_csv_file():
    """Check if quotes CSV file exists."""
    csv_path = Path("quotes.csv")
    
    if csv_path.exists():
        size_mb = csv_path.stat().st_size / (1024 * 1024)
        print(f"✅ Found quotes.csv ({size_mb:.1f} MB)")
        return True
    else:
        print("⚠️  quotes.csv not found in the root directory")
        print("   Please ensure your quotes CSV file is named 'quotes.csv'")
        print("   and placed in the root directory of the project.")
        return False


def run_basic_test():
    """Run a basic test to ensure everything works."""
    print("Running basic functionality test...")
    
    try:
        # Test help command
        result = subprocess.run([
            sys.executable, "src/main.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Basic functionality test passed!")
            return True
        else:
            print(f"❌ Basic test failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 Quote Generator Setup")
    print("=" * 40)
    
    # Change to project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    success = True
    
    # Step 1: Install dependencies
    if not install_dependencies():
        success = False
    
    print()
    
    # Step 2: Create directories
    create_directories()
    
    print()
    
    # Step 3: Check CSV file
    csv_exists = check_csv_file()
    
    print()
    
    # Step 4: Run basic test
    if csv_exists and not run_basic_test():
        success = False
    
    print()
    print("=" * 40)
    
    if success and csv_exists:
        print("🎉 Setup completed successfully!")
        print()
        print("You can now use the Quote Generator:")
        print("  python src/main.py --random")
        print("  python src/main.py --search 'love'")
        print("  python src/main.py --stats")
        print("  python src/main.py --help")
    elif success:
        print("⚠️  Setup completed with warnings.")
        print("   Please add your quotes.csv file to continue.")
    else:
        print("❌ Setup failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
