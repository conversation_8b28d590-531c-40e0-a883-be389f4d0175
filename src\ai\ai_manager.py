"""
AI Manager for coordinating quote generation capabilities.

This module provides a unified interface for AI quote generation,
automatically selecting between full ML models and simple fallback
generators based on available dependencies.
"""

import logging
from typing import List, Optional, Dict, Union
from pathlib import Path

from data.models import Quote
from utils.performance import timer

# Try to import full AI generator
try:
    from .quote_generator import Quote<PERSON><PERSON>enerator, DEPENDENCIES_AVAILABLE as FULL_AI_AVAILABLE
except ImportError:
    FULL_AI_AVAILABLE = False
    QuoteAIGenerator = None

# Import simple generator
from .simple_generator import SimpleQuoteGenerator


class AIQuoteManager:
    """
    Unified AI quote generation manager.
    
    Automatically selects the best available generation method based on
    installed dependencies and user preferences.
    """
    
    def __init__(self, model_dir: str = "data/ai_models", use_full_ai: bool = True):
        """
        Initialize the AI manager.
        
        Args:
            model_dir: Directory for AI models
            use_full_ai: Whether to prefer full AI when available
        """
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        self.use_full_ai = use_full_ai and FULL_AI_AVAILABLE
        
        # Initialize generators
        self.full_ai_generator = None
        self.simple_generator = SimpleQuoteGenerator()
        
        # State tracking
        self.is_trained = False
        self.training_data_size = 0
        
        self.logger.info(f"AI Manager initialized - Full AI: {self.use_full_ai}")
    
    def initialize_ai(self, quotes: List[Quote], force_retrain: bool = False) -> bool:
        """
        Initialize AI generators with quote data.
        
        Args:
            quotes: List of quotes for training/analysis
            force_retrain: Whether to force retraining even if model exists
            
        Returns:
            True if initialization successful
        """
        self.logger.info(f"Initializing AI with {len(quotes)} quotes...")
        
        success = True
        
        # Initialize simple generator (always available)
        try:
            self.simple_generator.analyze_quotes(quotes)
            self.logger.info("Simple generator initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize simple generator: {e}")
            success = False
        
        # Initialize full AI if available and requested
        if self.use_full_ai:
            try:
                self.full_ai_generator = QuoteAIGenerator(str(self.model_dir))
                
                # Try to load existing model first
                if not force_retrain and self.full_ai_generator.load_fine_tuned_model():
                    self.logger.info("Loaded existing fine-tuned model")
                    self.is_trained = True
                else:
                    # Load pre-trained model and prepare for training
                    if self.full_ai_generator.load_pretrained_model():
                        self.full_ai_generator.prepare_training_data(quotes)
                        self.logger.info("Full AI generator prepared for training")
                        # Note: Actual training should be done separately due to time requirements
                    else:
                        self.logger.warning("Failed to load pre-trained model")
                        self.use_full_ai = False
                
            except Exception as e:
                self.logger.error(f"Failed to initialize full AI: {e}")
                self.use_full_ai = False
        
        self.training_data_size = len(quotes)
        return success
    
    @timer
    def train_full_ai(self, epochs: int = 2, batch_size: int = 4) -> bool:
        """
        Train the full AI model (time-intensive operation).
        
        Args:
            epochs: Number of training epochs
            batch_size: Training batch size
            
        Returns:
            True if training successful
        """
        if not self.use_full_ai or not self.full_ai_generator:
            self.logger.warning("Full AI not available for training")
            return False
        
        try:
            self.logger.info("Starting full AI training (this may take a while)...")
            success = self.full_ai_generator.fine_tune_model(epochs, batch_size)
            
            if success:
                self.is_trained = True
                self.logger.info("Full AI training completed successfully")
            else:
                self.logger.error("Full AI training failed")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Training failed with error: {e}")
            return False
    
    def generate_random_quote(self) -> str:
        """Generate a random AI quote."""
        if self.use_full_ai and self.full_ai_generator and self.is_trained:
            try:
                quotes = self.full_ai_generator.generate_quote(num_return_sequences=1)
                if quotes:
                    return quotes[0]
            except Exception as e:
                self.logger.debug(f"Full AI generation failed, falling back: {e}")
        
        # Fallback to simple generator
        return self.simple_generator.generate_quote()
    
    def generate_category_quote(self, category: str, num_quotes: int = 1) -> List[str]:
        """
        Generate quotes for a specific category.
        
        Args:
            category: Category name
            num_quotes: Number of quotes to generate
            
        Returns:
            List of generated quotes
        """
        if self.use_full_ai and self.full_ai_generator and self.is_trained:
            try:
                quotes = self.full_ai_generator.generate_category_quote(category, num_quotes)
                if quotes:
                    return quotes
            except Exception as e:
                self.logger.debug(f"Full AI category generation failed, falling back: {e}")
        
        # Fallback to simple generator
        quotes = []
        for _ in range(num_quotes):
            quote = self.simple_generator.generate_category_quote(category)
            if quote:
                quotes.append(quote)
        
        return quotes
    
    def generate_author_style_quote(self, author: str, num_quotes: int = 1) -> List[str]:
        """
        Generate quotes in the style of a specific author.
        
        Args:
            author: Author name
            num_quotes: Number of quotes to generate
            
        Returns:
            List of generated quotes
        """
        if self.use_full_ai and self.full_ai_generator and self.is_trained:
            try:
                quotes = self.full_ai_generator.generate_author_style_quote(author, num_quotes)
                if quotes:
                    return quotes
            except Exception as e:
                self.logger.debug(f"Full AI author style generation failed, falling back: {e}")
        
        # Fallback to simple generator
        quotes = []
        for _ in range(num_quotes):
            quote = self.simple_generator.generate_author_style_quote(author)
            if quote:
                quotes.append(quote)
        
        return quotes
    
    def get_available_categories(self) -> List[str]:
        """Get list of categories available for generation."""
        if self.use_full_ai and self.full_ai_generator:
            stats = self.full_ai_generator.get_generation_statistics()
            return stats.get('top_categories', [])
        else:
            stats = self.simple_generator.get_generation_statistics()
            return stats.get('available_categories', [])
    
    def get_available_authors(self) -> List[str]:
        """Get list of authors available for style generation."""
        if self.use_full_ai and self.full_ai_generator:
            stats = self.full_ai_generator.get_generation_statistics()
            return stats.get('top_authors', [])
        else:
            stats = self.simple_generator.get_generation_statistics()
            return stats.get('available_authors', [])
    
    def get_ai_statistics(self) -> Dict[str, any]:
        """Get comprehensive AI statistics."""
        base_stats = {
            'full_ai_available': FULL_AI_AVAILABLE,
            'using_full_ai': self.use_full_ai,
            'is_trained': self.is_trained,
            'training_data_size': self.training_data_size,
            'generator_type': 'full_ai' if (self.use_full_ai and self.is_trained) else 'simple'
        }
        
        # Add generator-specific stats
        if self.use_full_ai and self.full_ai_generator:
            full_stats = self.full_ai_generator.get_generation_statistics()
            base_stats.update({
                'full_ai_stats': full_stats
            })
        
        simple_stats = self.simple_generator.get_generation_statistics()
        base_stats.update({
            'simple_ai_stats': simple_stats
        })
        
        return base_stats
    
    def is_ai_ready(self) -> bool:
        """Check if AI is ready for generation."""
        # Simple generator is always ready after initialization
        simple_ready = len(self.simple_generator.word_chains) > 0
        
        # Full AI is ready if trained
        full_ready = (self.use_full_ai and self.full_ai_generator and 
                     self.is_trained)
        
        return simple_ready or full_ready
    
    def get_generation_capabilities(self) -> Dict[str, bool]:
        """Get information about generation capabilities."""
        return {
            'random_generation': True,
            'category_generation': True,
            'author_style_generation': True,
            'full_ai_available': self.use_full_ai and self.is_trained,
            'simple_ai_available': len(self.simple_generator.word_chains) > 0,
            'can_train': FULL_AI_AVAILABLE
        }
    
    def save_state(self) -> bool:
        """Save the current AI state."""
        try:
            state_file = self.model_dir / "ai_state.json"
            
            import json
            state = {
                'is_trained': self.is_trained,
                'training_data_size': self.training_data_size,
                'use_full_ai': self.use_full_ai,
                'full_ai_available': FULL_AI_AVAILABLE
            }
            
            with open(state_file, 'w') as f:
                json.dump(state, f, indent=2)
            
            self.logger.info("AI state saved successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save AI state: {e}")
            return False
    
    def load_state(self) -> bool:
        """Load previously saved AI state."""
        try:
            state_file = self.model_dir / "ai_state.json"
            
            if not state_file.exists():
                return False
            
            import json
            with open(state_file, 'r') as f:
                state = json.load(f)
            
            self.is_trained = state.get('is_trained', False)
            self.training_data_size = state.get('training_data_size', 0)
            
            self.logger.info("AI state loaded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load AI state: {e}")
            return False
