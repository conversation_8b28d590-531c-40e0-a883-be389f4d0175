"""
AI-powered quote generation using transformer models.

This module implements machine learning-based quote generation that can create
original quotes in various styles and categories based on the training dataset.
"""

import logging
import random
import re
from typing import List, Optional, Dict, Tuple
from pathlib import Path
import pickle
import json

try:
    import torch
    from transformers import (
        GPT2LMHeadModel, GPT2Tokenizer, 
        TrainingArguments, Trainer,
        TextDataset, DataCollatorForLanguageModeling
    )
    from sentence_transformers import SentenceTransformer
    import nltk
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    DEPENDENCIES_AVAILABLE = False

from data.models import Quote, Author, Category, create_quote_from_csv_row
from utils.performance import timer, LRUCache


class QuoteAIGenerator:
    """
    AI-powered quote generator using transformer models.
    
    Features:
    - Fine-tuned GPT-2 model for quote generation
    - Category-aware generation
    - Author style mimicking
    - Quality filtering and validation
    """
    
    def __init__(self, model_dir: str = "data/ai_models", cache_size: int = 100):
        """
        Initialize the AI quote generator.
        
        Args:
            model_dir: Directory to store trained models
            cache_size: Size of generation cache
        """
        if not DEPENDENCIES_AVAILABLE:
            raise ImportError(
                "AI dependencies not available. Please install: "
                "pip install transformers torch sentence-transformers nltk scikit-learn"
            )
        
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        self.cache = LRUCache(cache_size)
        
        # Model components
        self.tokenizer = None
        self.model = None
        self.sentence_model = None
        
        # Training data
        self.quotes_data = []
        self.category_patterns = {}
        self.author_patterns = {}
        
        # Quality filters
        self.min_length = 10
        self.max_length = 200
        self.forbidden_words = set()
        
        # Initialize NLTK data
        self._init_nltk()
    
    def _init_nltk(self):
        """Initialize NLTK data downloads."""
        try:
            nltk.data.find('tokenizers/punkt')
            nltk.data.find('corpora/stopwords')
        except LookupError:
            self.logger.info("Downloading NLTK data...")
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
    
    def load_pretrained_model(self, model_name: str = "gpt2"):
        """
        Load a pre-trained model for fine-tuning.
        
        Args:
            model_name: Name of the pre-trained model
        """
        self.logger.info(f"Loading pre-trained model: {model_name}")
        
        try:
            self.tokenizer = GPT2Tokenizer.from_pretrained(model_name)
            self.model = GPT2LMHeadModel.from_pretrained(model_name)
            
            # Add padding token
            self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load sentence transformer for similarity
            self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            self.logger.info("Pre-trained model loaded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load pre-trained model: {e}")
            return False
    
    def prepare_training_data(self, quotes: List[Quote]) -> bool:
        """
        Prepare quotes data for training.
        
        Args:
            quotes: List of Quote objects
            
        Returns:
            True if preparation successful
        """
        self.logger.info(f"Preparing training data from {len(quotes)} quotes...")
        
        self.quotes_data = []
        category_texts = {}
        author_texts = {}
        
        for quote in quotes:
            # Clean and format quote text
            clean_text = self._clean_quote_text(quote.text)
            if not self._is_valid_quote(clean_text):
                continue
            
            # Create training format
            formatted_quote = f"<|startoftext|>{clean_text}<|endoftext|>"
            self.quotes_data.append(formatted_quote)
            
            # Group by categories
            for category in quote.categories:
                if category.name not in category_texts:
                    category_texts[category.name] = []
                category_texts[category.name].append(clean_text)
            
            # Group by authors
            author_name = quote.author.name
            if author_name not in author_texts:
                author_texts[author_name] = []
            author_texts[author_name].append(clean_text)
        
        # Analyze patterns
        self._analyze_category_patterns(category_texts)
        self._analyze_author_patterns(author_texts)
        
        self.logger.info(f"Prepared {len(self.quotes_data)} training samples")
        return len(self.quotes_data) > 0
    
    def _clean_quote_text(self, text: str) -> str:
        """Clean and normalize quote text."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove quotes if they wrap the entire text
        if text.startswith('"') and text.endswith('"'):
            text = text[1:-1]
        
        # Ensure proper sentence ending
        if not text.endswith(('.', '!', '?')):
            text += '.'
        
        return text
    
    def _is_valid_quote(self, text: str) -> bool:
        """Check if quote text is valid for training."""
        if len(text) < self.min_length or len(text) > self.max_length:
            return False
        
        # Check for forbidden content
        text_lower = text.lower()
        if any(word in text_lower for word in self.forbidden_words):
            return False
        
        # Check for reasonable word count
        words = word_tokenize(text)
        if len(words) < 3 or len(words) > 50:
            return False
        
        return True
    
    def _analyze_category_patterns(self, category_texts: Dict[str, List[str]]):
        """Analyze patterns in different categories."""
        self.logger.info("Analyzing category patterns...")
        
        for category, texts in category_texts.items():
            if len(texts) < 10:  # Skip categories with too few examples
                continue
            
            # Extract common words and phrases
            all_words = []
            for text in texts:
                words = word_tokenize(text.lower())
                all_words.extend(words)
            
            # Get word frequency
            word_freq = {}
            for word in all_words:
                if word.isalpha() and len(word) > 2:
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            # Store top words for this category
            top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20]
            self.category_patterns[category] = {
                'top_words': [word for word, freq in top_words],
                'sample_count': len(texts),
                'avg_length': sum(len(text) for text in texts) / len(texts)
            }
    
    def _analyze_author_patterns(self, author_texts: Dict[str, List[str]]):
        """Analyze patterns for different authors."""
        self.logger.info("Analyzing author patterns...")
        
        for author, texts in author_texts.items():
            if len(texts) < 5:  # Skip authors with too few quotes
                continue
            
            # Calculate average quote length
            avg_length = sum(len(text) for text in texts) / len(texts)
            
            # Extract common phrases
            all_text = ' '.join(texts).lower()
            
            self.author_patterns[author] = {
                'quote_count': len(texts),
                'avg_length': avg_length,
                'sample_texts': texts[:3]  # Store a few samples
            }
    
    @timer
    def fine_tune_model(self, epochs: int = 3, batch_size: int = 4, learning_rate: float = 5e-5):
        """
        Fine-tune the model on quote data.
        
        Args:
            epochs: Number of training epochs
            batch_size: Training batch size
            learning_rate: Learning rate for training
        """
        if not self.quotes_data:
            raise ValueError("No training data prepared. Call prepare_training_data first.")
        
        self.logger.info(f"Fine-tuning model with {len(self.quotes_data)} samples...")
        
        # Save training data to file
        train_file = self.model_dir / "train_data.txt"
        with open(train_file, 'w', encoding='utf-8') as f:
            for quote in self.quotes_data:
                f.write(quote + '\n')
        
        # Create dataset
        dataset = TextDataset(
            tokenizer=self.tokenizer,
            file_path=str(train_file),
            block_size=128
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False
        )
        
        # Training arguments
        training_args = TrainingArguments(
            output_dir=str(self.model_dir / "checkpoints"),
            overwrite_output_dir=True,
            num_train_epochs=epochs,
            per_device_train_batch_size=batch_size,
            save_steps=500,
            save_total_limit=2,
            prediction_loss_only=True,
            learning_rate=learning_rate,
            logging_steps=100,
            logging_dir=str(self.model_dir / "logs"),
        )
        
        # Create trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            data_collator=data_collator,
            train_dataset=dataset,
        )
        
        # Train the model
        try:
            trainer.train()
            
            # Save the fine-tuned model
            model_path = self.model_dir / "fine_tuned_model"
            self.model.save_pretrained(model_path)
            self.tokenizer.save_pretrained(model_path)
            
            # Save patterns
            patterns_path = self.model_dir / "patterns.pkl"
            with open(patterns_path, 'wb') as f:
                pickle.dump({
                    'category_patterns': self.category_patterns,
                    'author_patterns': self.author_patterns
                }, f)
            
            self.logger.info("Model fine-tuning completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Fine-tuning failed: {e}")
            return False
    
    def load_fine_tuned_model(self) -> bool:
        """Load a previously fine-tuned model."""
        model_path = self.model_dir / "fine_tuned_model"
        patterns_path = self.model_dir / "patterns.pkl"
        
        if not model_path.exists():
            self.logger.warning("No fine-tuned model found")
            return False
        
        try:
            self.logger.info("Loading fine-tuned model...")
            
            self.tokenizer = GPT2Tokenizer.from_pretrained(model_path)
            self.model = GPT2LMHeadModel.from_pretrained(model_path)
            self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            # Load patterns
            if patterns_path.exists():
                with open(patterns_path, 'rb') as f:
                    patterns = pickle.load(f)
                    self.category_patterns = patterns.get('category_patterns', {})
                    self.author_patterns = patterns.get('author_patterns', {})
            
            self.logger.info("Fine-tuned model loaded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load fine-tuned model: {e}")
            return False
    
    def generate_quote(self, prompt: str = "", max_length: int = 100, 
                      temperature: float = 0.8, num_return_sequences: int = 1) -> List[str]:
        """
        Generate quotes using the fine-tuned model.
        
        Args:
            prompt: Optional prompt to start generation
            max_length: Maximum length of generated text
            temperature: Sampling temperature (higher = more creative)
            num_return_sequences: Number of quotes to generate
            
        Returns:
            List of generated quote texts
        """
        if not self.model or not self.tokenizer:
            raise ValueError("Model not loaded. Load a model first.")
        
        # Check cache
        cache_key = f"{prompt}_{max_length}_{temperature}_{num_return_sequences}"
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # Prepare input
        input_text = f"<|startoftext|>{prompt}" if prompt else "<|startoftext|>"
        input_ids = self.tokenizer.encode(input_text, return_tensors='pt')
        
        # Generate
        with torch.no_grad():
            outputs = self.model.generate(
                input_ids,
                max_length=max_length,
                temperature=temperature,
                num_return_sequences=num_return_sequences,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        # Decode and clean results
        generated_quotes = []
        for output in outputs:
            text = self.tokenizer.decode(output, skip_special_tokens=True)
            
            # Remove prompt and clean
            if prompt:
                text = text.replace(prompt, "").strip()
            
            # Clean the generated text
            cleaned_text = self._post_process_generated_text(text)
            if cleaned_text and self._is_quality_quote(cleaned_text):
                generated_quotes.append(cleaned_text)
        
        # Cache results
        self.cache.put(cache_key, generated_quotes)
        
        return generated_quotes
    
    def _post_process_generated_text(self, text: str) -> str:
        """Post-process generated text to improve quality."""
        # Remove special tokens
        text = re.sub(r'<\|.*?\|>', '', text)
        
        # Clean whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Find the first complete sentence
        sentences = re.split(r'[.!?]+', text)
        if sentences:
            # Take the first non-empty sentence
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) > 10:
                    # Ensure proper capitalization
                    sentence = sentence[0].upper() + sentence[1:] if len(sentence) > 1 else sentence.upper()
                    
                    # Add period if needed
                    if not sentence.endswith(('.', '!', '?')):
                        sentence += '.'
                    
                    return sentence
        
        return ""
    
    def _is_quality_quote(self, text: str) -> bool:
        """Check if generated quote meets quality standards."""
        if not text or len(text) < 10 or len(text) > 300:
            return False
        
        # Check for repetitive patterns
        words = text.split()
        if len(set(words)) < len(words) * 0.7:  # Too much repetition
            return False
        
        # Check for reasonable sentence structure
        if not re.search(r'[a-zA-Z]', text):
            return False
        
        # Check for forbidden content
        text_lower = text.lower()
        if any(word in text_lower for word in self.forbidden_words):
            return False
        
        return True

    def generate_category_quote(self, category: str, num_quotes: int = 1) -> List[str]:
        """
        Generate quotes for a specific category.

        Args:
            category: Category name (e.g., "love", "wisdom")
            num_quotes: Number of quotes to generate

        Returns:
            List of generated quotes
        """
        category_lower = category.lower()

        # Get category-specific prompt
        prompt = self._get_category_prompt(category_lower)

        # Generate with category context
        quotes = self.generate_quote(
            prompt=prompt,
            max_length=80,
            temperature=0.7,
            num_return_sequences=num_quotes * 2  # Generate extra for filtering
        )

        # Filter and validate category relevance
        relevant_quotes = []
        for quote in quotes:
            if self._is_relevant_to_category(quote, category_lower):
                relevant_quotes.append(quote)
                if len(relevant_quotes) >= num_quotes:
                    break

        return relevant_quotes[:num_quotes]

    def generate_author_style_quote(self, author: str, num_quotes: int = 1) -> List[str]:
        """
        Generate quotes in the style of a specific author.

        Args:
            author: Author name
            num_quotes: Number of quotes to generate

        Returns:
            List of generated quotes in author's style
        """
        # Get author-specific prompt
        prompt = self._get_author_prompt(author)

        # Adjust generation parameters based on author patterns
        author_pattern = self.author_patterns.get(author, {})
        avg_length = author_pattern.get('avg_length', 60)

        # Generate with author context
        quotes = self.generate_quote(
            prompt=prompt,
            max_length=min(int(avg_length * 1.5), 120),
            temperature=0.6,  # Slightly lower for style consistency
            num_return_sequences=num_quotes * 2
        )

        # Filter for style consistency
        style_quotes = []
        for quote in quotes:
            if self._matches_author_style(quote, author):
                style_quotes.append(quote)
                if len(style_quotes) >= num_quotes:
                    break

        return style_quotes[:num_quotes]

    def _get_category_prompt(self, category: str) -> str:
        """Generate a prompt for category-specific generation."""
        category_pattern = self.category_patterns.get(category, {})

        if category_pattern:
            # Use top words from the category
            top_words = category_pattern.get('top_words', [])
            if top_words:
                # Create a prompt using category keywords
                selected_words = random.sample(top_words[:10], min(3, len(top_words[:10])))
                return f"About {category}, {', '.join(selected_words)}:"

        # Fallback prompts for common categories
        category_prompts = {
            'love': 'Love is',
            'wisdom': 'Wisdom teaches us',
            'life': 'Life is',
            'happiness': 'Happiness comes from',
            'success': 'Success requires',
            'friendship': 'True friendship',
            'courage': 'Courage means',
            'hope': 'Hope is',
            'truth': 'The truth is',
            'peace': 'Peace begins'
        }

        return category_prompts.get(category, f"About {category}:")

    def _get_author_prompt(self, author: str) -> str:
        """Generate a prompt for author-style generation."""
        author_pattern = self.author_patterns.get(author, {})

        if author_pattern and 'sample_texts' in author_pattern:
            # Use a fragment from the author's actual quotes
            sample_texts = author_pattern['sample_texts']
            if sample_texts:
                sample = random.choice(sample_texts)
                # Take first few words as prompt
                words = sample.split()[:3]
                return ' '.join(words)

        # Fallback: use author name context
        return f"In the style of {author}:"

    def _is_relevant_to_category(self, quote: str, category: str) -> bool:
        """Check if generated quote is relevant to the specified category."""
        if not self.sentence_model:
            return True  # Skip check if no sentence model

        try:
            # Get category keywords
            category_pattern = self.category_patterns.get(category, {})
            category_words = category_pattern.get('top_words', [category])

            # Create category context
            category_text = f"{category} " + " ".join(category_words[:5])

            # Calculate semantic similarity
            quote_embedding = self.sentence_model.encode([quote])
            category_embedding = self.sentence_model.encode([category_text])

            # Compute cosine similarity
            similarity = torch.cosine_similarity(
                torch.tensor(quote_embedding),
                torch.tensor(category_embedding)
            ).item()

            # Threshold for relevance
            return similarity > 0.3

        except Exception as e:
            self.logger.debug(f"Similarity check failed: {e}")
            return True  # Default to accepting if check fails

    def _matches_author_style(self, quote: str, author: str) -> bool:
        """Check if generated quote matches author's style."""
        author_pattern = self.author_patterns.get(author, {})

        if not author_pattern:
            return True  # Accept if no pattern data

        # Check length similarity
        avg_length = author_pattern.get('avg_length', 60)
        quote_length = len(quote)

        # Allow 50% variance in length
        if not (avg_length * 0.5 <= quote_length <= avg_length * 1.5):
            return False

        # Additional style checks could be added here
        # (vocabulary similarity, sentence structure, etc.)

        return True

    def get_generation_statistics(self) -> Dict[str, any]:
        """Get statistics about the AI generation capabilities."""
        return {
            'model_loaded': self.model is not None,
            'categories_analyzed': len(self.category_patterns),
            'authors_analyzed': len(self.author_patterns),
            'cache_size': self.cache.size(),
            'training_samples': len(self.quotes_data),
            'top_categories': list(self.category_patterns.keys())[:10],
            'top_authors': list(self.author_patterns.keys())[:10]
        }
