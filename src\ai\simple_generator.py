"""
Simple AI quote generator using template-based and Markov chain approaches.

This module provides a fallback AI generation system that doesn't require
heavy ML dependencies but can still create interesting quote variations.
"""

import random
import re
import logging
from typing import List, Dict, Set, Tuple
from collections import defaultdict, Counter
import json
from pathlib import Path

from data.models import Quote
from utils.performance import LRUCache


class SimpleQuoteGenerator:
    """
    Simple AI quote generator using template-based and Markov chain methods.
    
    This provides a lightweight alternative when full ML dependencies
    are not available or desired.
    """
    
    def __init__(self, cache_size: int = 50):
        """
        Initialize the simple generator.
        
        Args:
            cache_size: Size of generation cache
        """
        self.logger = logging.getLogger(__name__)
        self.cache = LRUCache(cache_size)
        
        # Data structures for generation
        self.word_chains = defaultdict(list)  # Markov chains
        self.category_templates = {}
        self.author_vocabularies = {}
        self.common_patterns = []
        
        # Quote components
        self.sentence_starters = []
        self.sentence_endings = []
        self.transition_words = []
        
        # Quality filters
        self.min_length = 15
        self.max_length = 200
    
    def analyze_quotes(self, quotes: List[Quote]) -> bool:
        """
        Analyze quotes to build generation patterns.
        
        Args:
            quotes: List of Quote objects to analyze
            
        Returns:
            True if analysis successful
        """
        self.logger.info(f"Analyzing {len(quotes)} quotes for pattern extraction...")
        
        category_texts = defaultdict(list)
        author_texts = defaultdict(list)
        all_texts = []
        
        for quote in quotes:
            text = self._clean_text(quote.text)
            if not text or len(text) < 10:
                continue
            
            all_texts.append(text)
            
            # Group by categories
            for category in quote.categories:
                category_texts[category.name].append(text)
            
            # Group by authors
            author_texts[quote.author.name].append(text)
        
        # Build Markov chains
        self._build_markov_chains(all_texts)
        
        # Extract templates and patterns
        self._extract_templates(category_texts)
        self._extract_author_vocabularies(author_texts)
        self._extract_common_patterns(all_texts)
        
        self.logger.info(f"Analysis complete: {len(self.word_chains)} word chains, "
                        f"{len(self.category_templates)} category templates")
        
        return True
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text."""
        # Remove quotes if they wrap the entire text
        if text.startswith('"') and text.endswith('"'):
            text = text[1:-1]
        
        # Clean whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Ensure proper sentence ending
        if not text.endswith(('.', '!', '?')):
            text += '.'
        
        return text
    
    def _build_markov_chains(self, texts: List[str]):
        """Build Markov chains from text data."""
        self.logger.info("Building Markov chains...")
        
        for text in texts:
            words = self._tokenize(text)
            
            # Build bigram chains
            for i in range(len(words) - 1):
                current_word = words[i].lower()
                next_word = words[i + 1]
                self.word_chains[current_word].append(next_word)
            
            # Extract sentence starters and endings
            if words:
                self.sentence_starters.append(words[0])
                if words[-1].endswith(('.', '!', '?')):
                    self.sentence_endings.append(words[-1])
    
    def _tokenize(self, text: str) -> List[str]:
        """Simple tokenization."""
        # Split on whitespace and punctuation
        words = re.findall(r'\b\w+\b|[.!?]', text)
        return [word for word in words if word]
    
    def _extract_templates(self, category_texts: Dict[str, List[str]]):
        """Extract templates for different categories."""
        self.logger.info("Extracting category templates...")
        
        for category, texts in category_texts.items():
            if len(texts) < 5:  # Skip categories with too few examples
                continue
            
            # Find common patterns
            patterns = []
            for text in texts[:20]:  # Limit for performance
                # Extract simple patterns
                words = self._tokenize(text)
                if len(words) >= 3:
                    # Create templates with placeholders
                    template = self._create_template(words)
                    if template:
                        patterns.append(template)
            
            if patterns:
                self.category_templates[category] = patterns[:10]  # Keep top 10
    
    def _create_template(self, words: List[str]) -> str:
        """Create a template from words by replacing some with placeholders."""
        if len(words) < 3:
            return ""
        
        # Replace nouns and adjectives with placeholders
        template_words = []
        for word in words:
            if word.lower() in ['is', 'are', 'was', 'were', 'the', 'a', 'an']:
                template_words.append(word)
            elif len(word) > 4 and word.isalpha():
                template_words.append('[WORD]')
            else:
                template_words.append(word)
        
        return ' '.join(template_words)
    
    def _extract_author_vocabularies(self, author_texts: Dict[str, List[str]]):
        """Extract vocabulary patterns for authors."""
        self.logger.info("Extracting author vocabularies...")
        
        for author, texts in author_texts.items():
            if len(texts) < 3:  # Skip authors with too few quotes
                continue
            
            # Extract common words
            all_words = []
            for text in texts:
                words = self._tokenize(text)
                all_words.extend([w.lower() for w in words if w.isalpha()])
            
            # Get word frequency
            word_freq = Counter(all_words)
            
            # Store top words for this author
            self.author_vocabularies[author] = {
                'top_words': [word for word, freq in word_freq.most_common(20)],
                'avg_length': sum(len(text) for text in texts) / len(texts),
                'quote_count': len(texts)
            }
    
    def _extract_common_patterns(self, texts: List[str]):
        """Extract common sentence patterns."""
        patterns = []
        
        for text in texts[:100]:  # Limit for performance
            words = self._tokenize(text)
            if 5 <= len(words) <= 15:  # Good length for patterns
                patterns.append(' '.join(words))
        
        # Store some patterns for inspiration
        self.common_patterns = random.sample(patterns, min(50, len(patterns)))
    
    def generate_quote(self, prompt: str = "", max_length: int = 100) -> str:
        """
        Generate a quote using Markov chains.
        
        Args:
            prompt: Optional starting prompt
            max_length: Maximum quote length
            
        Returns:
            Generated quote text
        """
        cache_key = f"general_{prompt}_{max_length}"
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # Generate using Markov chains
        if prompt:
            words = self._tokenize(prompt)
            if words:
                current_word = words[-1].lower()
            else:
                current_word = random.choice(list(self.word_chains.keys()))
        else:
            # Start with a random sentence starter
            if self.sentence_starters:
                current_word = random.choice(self.sentence_starters).lower()
            else:
                current_word = random.choice(list(self.word_chains.keys()))
        
        generated_words = [current_word.capitalize()]
        
        # Generate words using Markov chain
        for _ in range(max_length):
            if current_word in self.word_chains:
                next_words = self.word_chains[current_word]
                if next_words:
                    next_word = random.choice(next_words)
                    generated_words.append(next_word)
                    
                    # Check for sentence ending
                    if next_word.endswith(('.', '!', '?')):
                        break
                    
                    current_word = next_word.lower()
                else:
                    break
            else:
                # Try to find a related word
                similar_words = [w for w in self.word_chains.keys() 
                               if w.startswith(current_word[:3])]
                if similar_words:
                    current_word = random.choice(similar_words)
                else:
                    break
        
        # Join and clean
        quote = ' '.join(generated_words)
        quote = self._post_process_quote(quote)
        
        # Cache result
        self.cache.put(cache_key, quote)
        
        return quote
    
    def generate_category_quote(self, category: str) -> str:
        """Generate a quote for a specific category."""
        cache_key = f"category_{category}"
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        category_lower = category.lower()
        
        # Try to use category template
        if category_lower in self.category_templates:
            template = random.choice(self.category_templates[category_lower])
            quote = self._fill_template(template, category_lower)
        else:
            # Generate with category-specific prompt
            prompts = {
                'love': 'Love',
                'wisdom': 'Wisdom',
                'life': 'Life',
                'happiness': 'Happiness',
                'success': 'Success'
            }
            prompt = prompts.get(category_lower, category)
            quote = self.generate_quote(prompt=prompt)
        
        # Cache result
        self.cache.put(cache_key, quote)
        
        return quote
    
    def generate_author_style_quote(self, author: str) -> str:
        """Generate a quote in the style of a specific author."""
        cache_key = f"author_{author}"
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        author_vocab = self.author_vocabularies.get(author, {})
        
        if author_vocab:
            # Use author's vocabulary
            top_words = author_vocab.get('top_words', [])
            if top_words:
                # Start with one of the author's common words
                start_word = random.choice(top_words[:5])
                quote = self.generate_quote(prompt=start_word)
            else:
                quote = self.generate_quote()
        else:
            # Fallback to general generation
            quote = self.generate_quote()
        
        # Cache result
        self.cache.put(cache_key, quote)
        
        return quote
    
    def _fill_template(self, template: str, category: str) -> str:
        """Fill a template with appropriate words."""
        # Simple template filling
        words_for_category = {
            'love': ['heart', 'soul', 'passion', 'devotion', 'affection'],
            'wisdom': ['knowledge', 'understanding', 'insight', 'truth', 'experience'],
            'life': ['journey', 'adventure', 'experience', 'path', 'story'],
            'happiness': ['joy', 'contentment', 'bliss', 'delight', 'pleasure']
        }
        
        replacement_words = words_for_category.get(category, ['thing', 'matter', 'aspect'])
        
        # Replace placeholders
        result = template
        while '[WORD]' in result:
            word = random.choice(replacement_words)
            result = result.replace('[WORD]', word, 1)
        
        return self._post_process_quote(result)
    
    def _post_process_quote(self, quote: str) -> str:
        """Post-process generated quote."""
        # Clean up spacing
        quote = re.sub(r'\s+', ' ', quote.strip())
        
        # Ensure proper capitalization
        if quote:
            quote = quote[0].upper() + quote[1:] if len(quote) > 1 else quote.upper()
        
        # Ensure proper ending
        if quote and not quote.endswith(('.', '!', '?')):
            quote += '.'
        
        # Validate length
        if len(quote) < self.min_length or len(quote) > self.max_length:
            # Try to fix length issues
            if len(quote) < self.min_length:
                # Too short - try to extend
                if self.common_patterns:
                    pattern = random.choice(self.common_patterns)
                    quote = f"{quote} {pattern}"
            else:
                # Too long - truncate at sentence boundary
                sentences = re.split(r'[.!?]+', quote)
                if sentences:
                    quote = sentences[0].strip()
                    if not quote.endswith(('.', '!', '?')):
                        quote += '.'
        
        return quote
    
    def get_generation_statistics(self) -> Dict[str, any]:
        """Get statistics about the simple generator."""
        return {
            'word_chains': len(self.word_chains),
            'category_templates': len(self.category_templates),
            'author_vocabularies': len(self.author_vocabularies),
            'common_patterns': len(self.common_patterns),
            'cache_size': self.cache.size(),
            'available_categories': list(self.category_templates.keys()),
            'available_authors': list(self.author_vocabularies.keys())[:10]
        }
