"""
Efficient data loader for the quote generator application.

This module handles loading and processing of the large quotes CSV file
with performance optimizations including chunked processing, caching,
and memory-efficient data structures.
"""

import pandas as pd
import logging
from typing import List, Iterator, Optional, Dict, Set
from pathlib import Path
import pickle
import hashlib
import os
from tqdm import tqdm

from data.models import Quote, Author, Category, create_quote_from_csv_row


class DataLoader:
    """
    Efficient data loader for quotes CSV file.
    
    Features:
    - Chunked processing for memory efficiency
    - Caching mechanism to avoid reprocessing
    - Progress tracking for large datasets
    - Error handling and data validation
    """
    
    def __init__(self, csv_path: str, chunk_size: int = 10000, cache_dir: str = "data/cache"):
        """
        Initialize the data loader.
        
        Args:
            csv_path: Path to the quotes CSV file
            chunk_size: Number of rows to process at once
            cache_dir: Directory for caching processed data
        """
        self.csv_path = Path(csv_path)
        self.chunk_size = chunk_size
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Cache file path based on CSV file hash
        self._cache_file = self._get_cache_file_path()
        
        # Statistics
        self.total_quotes = 0
        self.valid_quotes = 0
        self.invalid_quotes = 0
        
    def _get_cache_file_path(self) -> Path:
        """
        Generate cache file path based on CSV file hash.
        
        Returns:
            Path to cache file
        """
        # Create hash of CSV file for cache invalidation
        csv_hash = self._get_file_hash(self.csv_path)
        return self.cache_dir / f"quotes_cache_{csv_hash}.pkl"
    
    def _get_file_hash(self, file_path: Path) -> str:
        """
        Calculate hash of file for cache validation.
        
        Args:
            file_path: Path to file
            
        Returns:
            MD5 hash of file
        """
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                # Read file in chunks to handle large files
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
        except FileNotFoundError:
            self.logger.error(f"File not found: {file_path}")
            return ""
        
        return hash_md5.hexdigest()[:12]
    
    def _is_cache_valid(self) -> bool:
        """
        Check if cached data is valid and up-to-date.
        
        Returns:
            True if cache is valid, False otherwise
        """
        return (self._cache_file.exists() and 
                self._cache_file.stat().st_mtime > self.csv_path.stat().st_mtime)
    
    def _save_to_cache(self, quotes: List[Quote]):
        """
        Save processed quotes to cache.
        
        Args:
            quotes: List of Quote objects to cache
        """
        try:
            with open(self._cache_file, 'wb') as f:
                pickle.dump({
                    'quotes': quotes,
                    'total_quotes': self.total_quotes,
                    'valid_quotes': self.valid_quotes,
                    'invalid_quotes': self.invalid_quotes
                }, f)
            self.logger.info(f"Cached {len(quotes)} quotes to {self._cache_file}")
        except Exception as e:
            self.logger.error(f"Failed to save cache: {e}")
    
    def _load_from_cache(self) -> Optional[List[Quote]]:
        """
        Load processed quotes from cache.
        
        Returns:
            List of Quote objects if cache is valid, None otherwise
        """
        try:
            with open(self._cache_file, 'rb') as f:
                data = pickle.load(f)
                self.total_quotes = data.get('total_quotes', 0)
                self.valid_quotes = data.get('valid_quotes', 0)
                self.invalid_quotes = data.get('invalid_quotes', 0)
                self.logger.info(f"Loaded {len(data['quotes'])} quotes from cache")
                return data['quotes']
        except Exception as e:
            self.logger.error(f"Failed to load cache: {e}")
            return None
    
    def _validate_csv_structure(self) -> bool:
        """
        Validate that CSV has required columns.
        
        Returns:
            True if CSV structure is valid, False otherwise
        """
        try:
            # Try different encodings for header validation
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            df_header = None

            for encoding in encodings:
                try:
                    df_header = pd.read_csv(self.csv_path, nrows=0, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue

            if df_header is None:
                self.logger.error("Could not read CSV header with any supported encoding")
                return False

            required_columns = {'quote', 'author', 'category'}
            
            if not required_columns.issubset(set(df_header.columns)):
                missing = required_columns - set(df_header.columns)
                self.logger.error(f"Missing required columns: {missing}")
                return False
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to validate CSV structure: {e}")
            return False
    
    def _process_chunk(self, chunk: pd.DataFrame) -> List[Quote]:
        """
        Process a chunk of CSV data into Quote objects.
        
        Args:
            chunk: DataFrame chunk to process
            
        Returns:
            List of valid Quote objects
        """
        quotes = []
        
        for _, row in chunk.iterrows():
            try:
                # Extract data from row
                text = str(row['quote']).strip()
                author_name = str(row['author']).strip()
                categories_str = str(row['category']).strip()
                
                # Skip empty quotes
                if not text or text.lower() in ['nan', 'none', '']:
                    self.invalid_quotes += 1
                    continue
                
                # Create quote object
                quote = create_quote_from_csv_row(text, author_name, categories_str)
                quotes.append(quote)
                self.valid_quotes += 1
                
            except Exception as e:
                self.logger.debug(f"Failed to process row: {e}")
                self.invalid_quotes += 1
                continue
        
        return quotes
    
    def load_quotes(self, use_cache: bool = True) -> List[Quote]:
        """
        Load all quotes from CSV file with caching support.
        
        Args:
            use_cache: Whether to use cached data if available
            
        Returns:
            List of Quote objects
            
        Raises:
            FileNotFoundError: If CSV file doesn't exist
            ValueError: If CSV structure is invalid
        """
        # Check if file exists
        if not self.csv_path.exists():
            raise FileNotFoundError(f"CSV file not found: {self.csv_path}")
        
        # Try to load from cache first
        if use_cache and self._is_cache_valid():
            cached_quotes = self._load_from_cache()
            if cached_quotes is not None:
                return cached_quotes
        
        # Validate CSV structure
        if not self._validate_csv_structure():
            raise ValueError("Invalid CSV structure")
        
        self.logger.info(f"Loading quotes from {self.csv_path}")
        
        # Reset statistics
        self.total_quotes = 0
        self.valid_quotes = 0
        self.invalid_quotes = 0
        
        quotes = []
        
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            chunk_iterator = None

            for encoding in encodings:
                try:
                    # Get total number of rows for progress tracking
                    with open(self.csv_path, 'r', encoding=encoding) as f:
                        total_rows = sum(1 for _ in f) - 1  # Subtract header
                    self.total_quotes = total_rows

                    # Process CSV in chunks with proper encoding
                    chunk_iterator = pd.read_csv(
                        self.csv_path,
                        chunksize=self.chunk_size,
                        dtype=str,  # Read all as strings to avoid type issues
                        na_filter=False,  # Don't convert to NaN
                        encoding=encoding,
                        on_bad_lines='skip'  # Skip problematic lines
                    )
                    self.logger.info(f"Successfully opened CSV with {encoding} encoding")
                    break
                except UnicodeDecodeError:
                    continue

            if chunk_iterator is None:
                raise ValueError("Could not decode CSV file with any supported encoding")
            
            # Process chunks with progress bar
            with tqdm(total=total_rows, desc="Loading quotes", unit="quotes") as pbar:
                for chunk in chunk_iterator:
                    chunk_quotes = self._process_chunk(chunk)
                    quotes.extend(chunk_quotes)
                    pbar.update(len(chunk))
            
            self.logger.info(f"Loaded {self.valid_quotes} valid quotes "
                           f"({self.invalid_quotes} invalid) from {self.total_quotes} total")
            
            # Save to cache
            if use_cache:
                self._save_to_cache(quotes)
            
            return quotes
            
        except Exception as e:
            self.logger.error(f"Failed to load quotes: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, int]:
        """
        Get loading statistics.
        
        Returns:
            Dictionary with loading statistics
        """
        return {
            'total_quotes': self.total_quotes,
            'valid_quotes': self.valid_quotes,
            'invalid_quotes': self.invalid_quotes,
            'success_rate': (self.valid_quotes / self.total_quotes * 100) if self.total_quotes > 0 else 0
        }
    
    def clear_cache(self):
        """Clear cached data."""
        try:
            if self._cache_file.exists():
                self._cache_file.unlink()
                self.logger.info("Cache cleared")
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {e}")


def get_unique_authors(quotes: List[Quote]) -> Set[str]:
    """
    Extract unique authors from quotes list.
    
    Args:
        quotes: List of Quote objects
        
    Returns:
        Set of unique author names
    """
    return {quote.author.name for quote in quotes}


def get_unique_categories(quotes: List[Quote]) -> Set[str]:
    """
    Extract unique categories from quotes list.
    
    Args:
        quotes: List of Quote objects
        
    Returns:
        Set of unique category names
    """
    categories = set()
    for quote in quotes:
        categories.update(cat.name for cat in quote.categories)
    return categories
