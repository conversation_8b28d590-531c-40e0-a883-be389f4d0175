"""
Core quote management functionality for the quote generator application.

This module provides the main QuoteManager class that handles quote operations
including random selection, filtering, search, and user interactions with
performance optimizations for large datasets.
"""

import random
import logging
from typing import List, Optional, Dict, Set, Callable
from collections import defaultdict, Counter
import bisect

from data.models import Quote, Author, Category
from core.data_loader import DataLoader, get_unique_authors, get_unique_categories

# Try to import AI capabilities
try:
    from ai.ai_manager import AIQuoteManager
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    AIQuoteManager = None


class QuoteManager:
    """
    Main quote management class with efficient operations.
    
    Features:
    - Random quote generation with weighted selection
    - Fast filtering by author, category, rating
    - Search functionality across all quote fields
    - User interaction tracking (favorites, ratings, views)
    - Performance-optimized data structures
    """
    
    def __init__(self, csv_path: str, chunk_size: int = 10000):
        """
        Initialize the quote manager.
        
        Args:
            csv_path: Path to the quotes CSV file
            chunk_size: Chunk size for data loading
        """
        self.data_loader = DataLoader(csv_path, chunk_size)
        self.quotes: List[Quote] = []
        self.logger = logging.getLogger(__name__)
        
        # Performance optimization indexes
        self._author_index: Dict[str, List[int]] = defaultdict(list)
        self._category_index: Dict[str, List[int]] = defaultdict(list)
        self._rating_index: Dict[float, List[int]] = defaultdict(list)
        self._favorite_index: List[int] = []
        
        # Statistics
        self._view_history: List[str] = []
        self._search_cache: Dict[str, List[int]] = {}
        
        # Random selection weights
        self._selection_weights: List[float] = []

        # AI generation capabilities
        self.ai_manager: Optional[AIQuoteManager] = None
        self._ai_initialized = False
        
    def load_quotes(self, use_cache: bool = True) -> int:
        """
        Load quotes from CSV file and build indexes.
        
        Args:
            use_cache: Whether to use cached data
            
        Returns:
            Number of quotes loaded
        """
        self.logger.info("Loading quotes...")
        self.quotes = self.data_loader.load_quotes(use_cache)
        
        # Build performance indexes
        self._build_indexes()
        
        # Initialize selection weights
        self._initialize_weights()

        # Initialize AI capabilities
        self._initialize_ai()

        self.logger.info(f"Loaded {len(self.quotes)} quotes with indexes")
        return len(self.quotes)
    
    def _build_indexes(self):
        """Build performance indexes for fast filtering."""
        self.logger.info("Building performance indexes...")
        
        # Clear existing indexes
        self._author_index.clear()
        self._category_index.clear()
        self._rating_index.clear()
        self._favorite_index.clear()
        
        for i, quote in enumerate(self.quotes):
            # Author index
            author_key = quote.author.normalized_name
            self._author_index[author_key].append(i)
            
            # Category index
            for category in quote.categories:
                self._category_index[category.name].append(i)
            
            # Rating index
            if quote.rating is not None:
                self._rating_index[quote.rating].append(i)
            
            # Favorite index
            if quote.is_favorite:
                self._favorite_index.append(i)
    
    def _initialize_weights(self):
        """Initialize selection weights for random quote generation."""
        # Base weight for all quotes
        base_weight = 1.0
        
        self._selection_weights = []
        for quote in self.quotes:
            weight = base_weight
            
            # Boost weight for favorites
            if quote.is_favorite:
                weight *= 1.5
            
            # Boost weight for highly rated quotes
            if quote.rating is not None and quote.rating >= 4.0:
                weight *= 1.3
            
            # Slightly reduce weight for frequently viewed quotes
            if quote.view_count > 10:
                weight *= 0.9
            
            self._selection_weights.append(weight)

    def _initialize_ai(self):
        """Initialize AI generation capabilities."""
        if not AI_AVAILABLE:
            self.logger.info("AI capabilities not available")
            return

        try:
            self.logger.info("Initializing AI quote generation...")
            self.ai_manager = AIQuoteManager()

            # Initialize with current quotes
            if self.quotes:
                success = self.ai_manager.initialize_ai(self.quotes)
                if success:
                    self._ai_initialized = True
                    self.logger.info("AI initialization completed")
                else:
                    self.logger.warning("AI initialization failed")

        except Exception as e:
            self.logger.error(f"Failed to initialize AI: {e}")
            self.ai_manager = None
    
    def get_random_quote(self, weighted: bool = True) -> Optional[Quote]:
        """
        Get a random quote with optional weighted selection.
        
        Args:
            weighted: Whether to use weighted random selection
            
        Returns:
            Random Quote object or None if no quotes available
        """
        if not self.quotes:
            return None
        
        if weighted and self._selection_weights:
            # Weighted random selection
            quote = random.choices(self.quotes, weights=self._selection_weights, k=1)[0]
        else:
            # Simple random selection
            quote = random.choice(self.quotes)
        
        # Track view and update weights
        quote.increment_view_count()
        self._view_history.append(quote.id)
        
        # Keep view history limited
        if len(self._view_history) > 1000:
            self._view_history = self._view_history[-500:]
        
        return quote
    
    def filter_by_author(self, author_name: str) -> List[Quote]:
        """
        Filter quotes by author name.
        
        Args:
            author_name: Author name to filter by (case-insensitive)
            
        Returns:
            List of quotes by the specified author
        """
        normalized_name = author_name.strip().lower()
        quote_indices = self._author_index.get(normalized_name, [])
        return [self.quotes[i] for i in quote_indices]
    
    def filter_by_category(self, category_name: str) -> List[Quote]:
        """
        Filter quotes by category.
        
        Args:
            category_name: Category name to filter by (case-insensitive)
            
        Returns:
            List of quotes in the specified category
        """
        normalized_name = category_name.strip().lower()
        quote_indices = self._category_index.get(normalized_name, [])
        return [self.quotes[i] for i in quote_indices]
    
    def filter_by_rating(self, min_rating: float, max_rating: float = 5.0) -> List[Quote]:
        """
        Filter quotes by rating range.
        
        Args:
            min_rating: Minimum rating (inclusive)
            max_rating: Maximum rating (inclusive)
            
        Returns:
            List of quotes within the rating range
        """
        result_indices = set()
        
        for rating, indices in self._rating_index.items():
            if min_rating <= rating <= max_rating:
                result_indices.update(indices)
        
        return [self.quotes[i] for i in sorted(result_indices)]
    
    def get_favorites(self) -> List[Quote]:
        """
        Get all favorite quotes.
        
        Returns:
            List of favorite quotes
        """
        return [self.quotes[i] for i in self._favorite_index]
    
    def search_quotes(self, search_term: str, limit: Optional[int] = None) -> List[Quote]:
        """
        Search quotes by text, author, or category.
        
        Args:
            search_term: Term to search for
            limit: Maximum number of results to return
            
        Returns:
            List of matching quotes
        """
        if not search_term.strip():
            return []
        
        # Check cache first
        cache_key = search_term.lower().strip()
        if cache_key in self._search_cache:
            indices = self._search_cache[cache_key]
            results = [self.quotes[i] for i in indices]
        else:
            # Perform search
            matching_indices = []
            for i, quote in enumerate(self.quotes):
                if quote.matches_search(search_term):
                    matching_indices.append(i)
            
            # Cache results
            self._search_cache[cache_key] = matching_indices
            results = [self.quotes[i] for i in matching_indices]
            
            # Limit cache size
            if len(self._search_cache) > 100:
                # Remove oldest entries
                oldest_keys = list(self._search_cache.keys())[:50]
                for key in oldest_keys:
                    del self._search_cache[key]
        
        # Apply limit if specified
        if limit is not None:
            results = results[:limit]
        
        return results
    
    def get_quote_by_id(self, quote_id: str) -> Optional[Quote]:
        """
        Get a quote by its ID.
        
        Args:
            quote_id: Quote ID to search for
            
        Returns:
            Quote object if found, None otherwise
        """
        for quote in self.quotes:
            if quote.id == quote_id:
                return quote
        return None
    
    def toggle_favorite(self, quote_id: str) -> bool:
        """
        Toggle favorite status of a quote.
        
        Args:
            quote_id: ID of quote to toggle
            
        Returns:
            True if operation successful, False otherwise
        """
        quote = self.get_quote_by_id(quote_id)
        if quote:
            quote.toggle_favorite()
            # Update favorite index
            quote_index = next(i for i, q in enumerate(self.quotes) if q.id == quote_id)
            
            if quote.is_favorite and quote_index not in self._favorite_index:
                self._favorite_index.append(quote_index)
            elif not quote.is_favorite and quote_index in self._favorite_index:
                self._favorite_index.remove(quote_index)
            
            # Update weights
            self._initialize_weights()
            return True
        return False
    
    def rate_quote(self, quote_id: str, rating: float) -> bool:
        """
        Rate a quote.
        
        Args:
            quote_id: ID of quote to rate
            rating: Rating value (0-5)
            
        Returns:
            True if operation successful, False otherwise
        """
        quote = self.get_quote_by_id(quote_id)
        if quote:
            try:
                old_rating = quote.rating
                quote.set_rating(rating)
                
                # Update rating index
                quote_index = next(i for i, q in enumerate(self.quotes) if q.id == quote_id)
                
                # Remove from old rating
                if old_rating is not None and quote_index in self._rating_index[old_rating]:
                    self._rating_index[old_rating].remove(quote_index)
                
                # Add to new rating
                self._rating_index[rating].append(quote_index)
                
                # Update weights
                self._initialize_weights()
                return True
            except ValueError:
                return False
        return False
    
    def get_statistics(self) -> Dict[str, any]:
        """
        Get comprehensive statistics about the quote collection.
        
        Returns:
            Dictionary with various statistics
        """
        if not self.quotes:
            return {}
        
        # Basic statistics
        total_quotes = len(self.quotes)
        favorite_count = len(self._favorite_index)
        rated_quotes = sum(1 for q in self.quotes if q.rating is not None)
        
        # Author statistics
        unique_authors = len(self._author_index)
        author_counts = Counter(q.author.name for q in self.quotes)
        top_authors = author_counts.most_common(5)
        
        # Category statistics
        unique_categories = len(self._category_index)
        category_counts = Counter()
        for quote in self.quotes:
            for cat in quote.categories:
                category_counts[cat.name] += 1
        top_categories = category_counts.most_common(10)
        
        # Rating statistics
        ratings = [q.rating for q in self.quotes if q.rating is not None]
        avg_rating = sum(ratings) / len(ratings) if ratings else 0
        
        # View statistics
        total_views = sum(q.view_count for q in self.quotes)
        
        return {
            'total_quotes': total_quotes,
            'unique_authors': unique_authors,
            'unique_categories': unique_categories,
            'favorite_count': favorite_count,
            'rated_quotes': rated_quotes,
            'average_rating': round(avg_rating, 2),
            'total_views': total_views,
            'top_authors': top_authors,
            'top_categories': top_categories,
            'recent_views': len(self._view_history)
        }

    # AI Generation Methods

    def generate_ai_quote(self) -> Optional[str]:
        """
        Generate an AI-powered quote.

        Returns:
            Generated quote text or None if AI not available
        """
        if not self._ai_initialized or not self.ai_manager:
            self.logger.warning("AI generation not available")
            return None

        try:
            return self.ai_manager.generate_random_quote()
        except Exception as e:
            self.logger.error(f"AI quote generation failed: {e}")
            return None

    def generate_ai_category_quote(self, category: str, num_quotes: int = 1) -> List[str]:
        """
        Generate AI quotes for a specific category.

        Args:
            category: Category name
            num_quotes: Number of quotes to generate

        Returns:
            List of generated quotes
        """
        if not self._ai_initialized or not self.ai_manager:
            self.logger.warning("AI generation not available")
            return []

        try:
            return self.ai_manager.generate_category_quote(category, num_quotes)
        except Exception as e:
            self.logger.error(f"AI category quote generation failed: {e}")
            return []

    def generate_ai_author_style_quote(self, author: str, num_quotes: int = 1) -> List[str]:
        """
        Generate AI quotes in the style of a specific author.

        Args:
            author: Author name
            num_quotes: Number of quotes to generate

        Returns:
            List of generated quotes
        """
        if not self._ai_initialized or not self.ai_manager:
            self.logger.warning("AI generation not available")
            return []

        try:
            return self.ai_manager.generate_author_style_quote(author, num_quotes)
        except Exception as e:
            self.logger.error(f"AI author style quote generation failed: {e}")
            return []

    def train_ai_model(self, epochs: int = 2, batch_size: int = 4) -> bool:
        """
        Train the AI model (time-intensive operation).

        Args:
            epochs: Number of training epochs
            batch_size: Training batch size

        Returns:
            True if training successful
        """
        if not self._ai_initialized or not self.ai_manager:
            self.logger.warning("AI not available for training")
            return False

        try:
            return self.ai_manager.train_full_ai(epochs, batch_size)
        except Exception as e:
            self.logger.error(f"AI training failed: {e}")
            return False

    def get_ai_statistics(self) -> Dict[str, any]:
        """
        Get AI generation statistics.

        Returns:
            Dictionary with AI statistics
        """
        if not self.ai_manager:
            return {'ai_available': False}

        try:
            stats = self.ai_manager.get_ai_statistics()
            stats['ai_available'] = True
            stats['ai_initialized'] = self._ai_initialized
            return stats
        except Exception as e:
            self.logger.error(f"Failed to get AI statistics: {e}")
            return {'ai_available': False, 'error': str(e)}

    def is_ai_available(self) -> bool:
        """Check if AI generation is available."""
        return AI_AVAILABLE and self._ai_initialized and self.ai_manager is not None

    def get_ai_capabilities(self) -> Dict[str, bool]:
        """Get information about AI capabilities."""
        if not self.ai_manager:
            return {
                'ai_available': False,
                'random_generation': False,
                'category_generation': False,
                'author_style_generation': False,
                'can_train': False
            }

        try:
            capabilities = self.ai_manager.get_generation_capabilities()
            capabilities['ai_available'] = True
            return capabilities
        except Exception as e:
            self.logger.error(f"Failed to get AI capabilities: {e}")
            return {'ai_available': False, 'error': str(e)}
