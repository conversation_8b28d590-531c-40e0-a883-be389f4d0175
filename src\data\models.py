"""
Data models for the quote generator application.

This module defines the core data structures used throughout the application,
including Quote, Author, and Category models with efficient memory usage.
"""

from dataclasses import dataclass
from typing import List, Optional, Set
import hashlib


@dataclass(frozen=True)
class Author:
    """
    Represents a quote author with efficient string handling.
    
    Uses frozen dataclass for immutability and memory efficiency.
    """
    name: str
    
    def __post_init__(self):
        """Validate author name on creation."""
        if not self.name or not self.name.strip():
            raise ValueError("Author name cannot be empty")
    
    @property
    def normalized_name(self) -> str:
        """Return normalized author name for comparison."""
        return self.name.strip().lower()
    
    def __str__(self) -> str:
        return self.name


@dataclass(frozen=True)
class Category:
    """
    Represents a quote category with normalized handling.
    
    Categories are case-insensitive and whitespace-normalized.
    """
    name: str
    
    def __post_init__(self):
        """Validate and normalize category name on creation."""
        if not self.name or not self.name.strip():
            raise ValueError("Category name cannot be empty")
        # Normalize the category name
        object.__setattr__(self, 'name', self.name.strip().lower())
    
    def __str__(self) -> str:
        return self.name


@dataclass
class Quote:
    """
    Represents a quote with associated metadata.
    
    Optimized for memory usage while maintaining functionality for
    search, filtering, and user interactions.
    """
    text: str
    author: Author
    categories: Set[Category]
    rating: Optional[float] = None
    is_favorite: bool = False
    view_count: int = 0
    
    def __post_init__(self):
        """Validate quote data on creation."""
        if not self.text or not self.text.strip():
            raise ValueError("Quote text cannot be empty")
        
        # Ensure categories is a set for efficient operations
        if isinstance(self.categories, (list, tuple)):
            self.categories = set(self.categories)
        
        # Validate rating if provided
        if self.rating is not None:
            if not (0 <= self.rating <= 5):
                raise ValueError("Rating must be between 0 and 5")
    
    @property
    def id(self) -> str:
        """
        Generate a unique identifier for the quote.
        
        Uses hash of text and author for consistent identification.
        """
        content = f"{self.text.strip()}{self.author.name}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    @property
    def category_names(self) -> List[str]:
        """Return sorted list of category names."""
        return sorted([cat.name for cat in self.categories])
    
    @property
    def word_count(self) -> int:
        """Return word count of the quote text."""
        return len(self.text.split())
    
    def has_category(self, category_name: str) -> bool:
        """
        Check if quote has a specific category.
        
        Args:
            category_name: Category name to check (case-insensitive)
            
        Returns:
            True if quote has the category, False otherwise
        """
        normalized_name = category_name.strip().lower()
        return any(cat.name == normalized_name for cat in self.categories)
    
    def matches_search(self, search_term: str) -> bool:
        """
        Check if quote matches a search term.
        
        Searches in quote text, author name, and categories.
        
        Args:
            search_term: Term to search for (case-insensitive)
            
        Returns:
            True if quote matches search term, False otherwise
        """
        search_lower = search_term.lower().strip()
        
        # Search in quote text
        if search_lower in self.text.lower():
            return True
        
        # Search in author name
        if search_lower in self.author.normalized_name:
            return True
        
        # Search in categories
        return any(search_lower in cat.name for cat in self.categories)
    
    def increment_view_count(self):
        """Increment the view count for this quote."""
        self.view_count += 1
    
    def set_rating(self, rating: float):
        """
        Set the rating for this quote.
        
        Args:
            rating: Rating value between 0 and 5
            
        Raises:
            ValueError: If rating is not between 0 and 5
        """
        if not (0 <= rating <= 5):
            raise ValueError("Rating must be between 0 and 5")
        self.rating = rating
    
    def toggle_favorite(self):
        """Toggle the favorite status of this quote."""
        self.is_favorite = not self.is_favorite
    
    def __str__(self) -> str:
        """Return formatted string representation of the quote."""
        return f'"{self.text}" - {self.author.name}'
    
    def __repr__(self) -> str:
        """Return detailed string representation for debugging."""
        return (f"Quote(id={self.id}, author={self.author.name}, "
                f"categories={len(self.categories)}, rating={self.rating})")


def create_quote_from_csv_row(text: str, author_name: str, categories_str: str) -> Quote:
    """
    Create a Quote object from CSV row data.
    
    Args:
        text: Quote text
        author_name: Author name
        categories_str: Comma-separated category string
        
    Returns:
        Quote object with parsed data
        
    Raises:
        ValueError: If required data is missing or invalid
    """
    # Create author
    author = Author(name=author_name.strip())
    
    # Parse categories
    categories = set()
    if categories_str and categories_str.strip():
        category_names = [name.strip() for name in categories_str.split(',')]
        categories = {Category(name=name) for name in category_names if name}
    
    # Create and return quote
    return Quote(
        text=text.strip(),
        author=author,
        categories=categories
    )
