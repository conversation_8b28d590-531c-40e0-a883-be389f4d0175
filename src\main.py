"""
Main entry point for the Quote Generator application.

This module provides the main function and basic CLI interface for
the quote generator application.
"""

import sys
import argparse
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import settings
from config.logging_config import setup_logging, get_logger
from core.quote_manager import QuoteManager
from utils.formatters import Quote<PERSON>ormatter, StatisticsFormatter, format_error, format_success, format_info
from utils.performance import performance_monitor, get_memory_usage


def setup_application():
    """Initialize the application with necessary setup."""
    # Create required directories
    settings.create_directories()
    
    # Setup logging
    setup_logging()
    
    logger = get_logger(__name__)
    logger.info("Quote Generator application starting...")
    
    return logger


def create_quote_manager(csv_path: str = None) -> QuoteManager:
    """
    Create and initialize quote manager.
    
    Args:
        csv_path: Path to CSV file (optional)
        
    Returns:
        Initialized QuoteManager instance
    """
    if csv_path is None:
        csv_path = str(settings.get_csv_path())
    
    logger = get_logger(__name__)
    logger.info(f"Initializing quote manager with CSV: {csv_path}")
    
    quote_manager = QuoteManager(csv_path, settings.DEFAULT_CHUNK_SIZE)
    
    # Load quotes
    try:
        quote_count = quote_manager.load_quotes()
        logger.info(f"Successfully loaded {quote_count} quotes")
        return quote_manager
    except Exception as e:
        logger.error(f"Failed to load quotes: {e}")
        raise


def main():
    """Main application function."""
    parser = argparse.ArgumentParser(
        description="Advanced Quote Generator - Generate, search, and manage quotes",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                          # Interactive mode
  python main.py --random                 # Get a random quote
  python main.py --search "love"          # Search for quotes about love
  python main.py --author "Einstein"      # Quotes by Einstein
  python main.py --category "inspirational" # Inspirational quotes
  python main.py --stats                  # Show statistics
        """
    )
    
    # File options
    parser.add_argument(
        "--csv", 
        type=str, 
        help="Path to quotes CSV file"
    )
    
    # Action options
    parser.add_argument(
        "--random", 
        action="store_true", 
        help="Get a random quote"
    )
    
    parser.add_argument(
        "--search", 
        type=str, 
        help="Search quotes by text, author, or category"
    )
    
    parser.add_argument(
        "--author", 
        type=str, 
        help="Filter quotes by author"
    )
    
    parser.add_argument(
        "--category", 
        type=str, 
        help="Filter quotes by category"
    )
    
    parser.add_argument(
        "--stats", 
        action="store_true", 
        help="Show collection statistics"
    )
    
    # Display options
    parser.add_argument(
        "--limit", 
        type=int, 
        default=10, 
        help="Maximum number of quotes to display (default: 10)"
    )
    
    parser.add_argument(
        "--no-color", 
        action="store_true", 
        help="Disable colored output"
    )
    
    parser.add_argument(
        "--compact", 
        action="store_true", 
        help="Use compact display format"
    )
    
    # Debug options
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="Enable debug mode"
    )
    
    parser.add_argument(
        "--memory", 
        action="store_true", 
        help="Show memory usage information"
    )
    
    args = parser.parse_args()
    
    try:
        # Setup application
        logger = setup_application()
        
        # Enable debug logging if requested
        if args.debug:
            import logging
            logging.getLogger().setLevel(logging.DEBUG)
            logger.debug("Debug mode enabled")
        
        # Create formatters
        colored = not args.no_color and settings.ENABLE_COLORED_OUTPUT
        quote_formatter = QuoteFormatter(width=settings.DEFAULT_TERMINAL_WIDTH)
        stats_formatter = StatisticsFormatter(colored=colored)
        
        # Show memory usage if requested
        if args.memory:
            memory_info = get_memory_usage()
            print(format_info(f"Memory usage: {memory_info['rss']:.1f} MB", colored))
        
        # Create quote manager
        print(format_info("Loading quotes...", colored))
        quote_manager = create_quote_manager(args.csv)
        print(format_success(f"Loaded {len(quote_manager.quotes)} quotes", colored))
        
        # Handle different actions
        if args.random:
            # Get random quote
            quote = quote_manager.get_random_quote()
            if quote:
                print("\n" + quote_formatter.format_quote(quote, colored=colored))
            else:
                print(format_error("No quotes available", colored))
        
        elif args.search:
            # Search quotes
            quotes = quote_manager.search_quotes(args.search, limit=args.limit)
            print(f"\n{stats_formatter.format_search_results_header(args.search, len(quotes))}")
            
            if quotes:
                if args.compact:
                    for i, quote in enumerate(quotes, 1):
                        print(f"{i:2d}. {quote_formatter.format_compact_quote(quote, colored)}")
                else:
                    print("\n" + quote_formatter.format_quote_list(quotes, colored=colored))
            else:
                print(format_info("No quotes found matching your search", colored))
        
        elif args.author:
            # Filter by author
            quotes = quote_manager.filter_by_author(args.author)
            if len(quotes) > args.limit:
                quotes = quotes[:args.limit]
            
            print(f"\n{stats_formatter.format_filter_header('author', args.author, len(quotes))}")
            
            if quotes:
                if args.compact:
                    for i, quote in enumerate(quotes, 1):
                        print(f"{i:2d}. {quote_formatter.format_compact_quote(quote, colored)}")
                else:
                    print("\n" + quote_formatter.format_quote_list(quotes, colored=colored))
            else:
                print(format_info(f"No quotes found by author '{args.author}'", colored))
        
        elif args.category:
            # Filter by category
            quotes = quote_manager.filter_by_category(args.category)
            if len(quotes) > args.limit:
                quotes = quotes[:args.limit]
            
            print(f"\n{stats_formatter.format_filter_header('category', args.category, len(quotes))}")
            
            if quotes:
                if args.compact:
                    for i, quote in enumerate(quotes, 1):
                        print(f"{i:2d}. {quote_formatter.format_compact_quote(quote, colored)}")
                else:
                    print("\n" + quote_formatter.format_quote_list(quotes, colored=colored))
            else:
                print(format_info(f"No quotes found in category '{args.category}'", colored))
        
        elif args.stats:
            # Show statistics
            stats = quote_manager.get_statistics()
            print("\n" + stats_formatter.format_statistics(stats))
            
            # Show performance statistics if debug mode
            if args.debug:
                perf_report = performance_monitor.get_performance_report()
                if perf_report:
                    print(f"\n{format_info('Performance Report:', colored)}")
                    for operation, metrics in perf_report.items():
                        print(f"  {operation}: {metrics['average']:.4f}s avg ({metrics['count']} calls)")
        
        else:
            # Interactive mode (basic for now)
            print(format_info("Interactive mode - showing random quote", colored))
            quote = quote_manager.get_random_quote()
            if quote:
                print("\n" + quote_formatter.format_quote(quote, colored=colored))
                print(f"\n{format_info('Use --help for more options', colored)}")
            else:
                print(format_error("No quotes available", colored))
        
        # Show final memory usage if requested
        if args.memory:
            memory_info = get_memory_usage()
            memory_msg = f'Final memory usage: {memory_info["rss"]:.1f} MB'
            print(f"\n{format_info(memory_msg, colored)}")
    
    except KeyboardInterrupt:
        print(f"\n{format_info('Operation cancelled by user', colored=True)}")
        sys.exit(0)
    except Exception as e:
        logger = get_logger(__name__)
        logger.error(f"Application error: {e}")
        print(format_error(f"Application error: {e}", colored=True))
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
