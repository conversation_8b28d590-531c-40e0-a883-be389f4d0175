"""
Unit tests for AI quote generation functionality.

This module tests the AI generation capabilities including both
full AI and simple fallback generators.
"""

import unittest
import tempfile
import csv
from pathlib import Path
import sys

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from data.models import Quote, Author, Category, create_quote_from_csv_row
from ai.simple_generator import SimpleQuoteGenerator

# Try to import AI manager
try:
    from ai.ai_manager import AIQuoteManager
    AI_MANAGER_AVAILABLE = True
except ImportError:
    AI_MANAGER_AVAILABLE = False
    AIQuoteManager = None


class TestSimpleQuoteGenerator(unittest.TestCase):
    """Test cases for SimpleQuoteGenerator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.generator = SimpleQuoteGenerator()
        
        # Create test quotes
        self.test_quotes = [
            create_quote_from_csv_row(
                "Life is what happens to you while you're busy making other plans.",
                "<PERSON>",
                "life, wisdom"
            ),
            create_quote_from_csv_row(
                "The only way to do great work is to love what you do.",
                "<PERSON> Jobs",
                "work, inspiration"
            ),
            create_quote_from_csv_row(
                "Love is the bridge between two hearts.",
                "Unknown",
                "love, relationships"
            ),
            create_quote_from_csv_row(
                "Wisdom comes from experience and experience comes from mistakes.",
                "Unknown",
                "wisdom, learning"
            ),
            create_quote_from_csv_row(
                "Success is not final, failure is not fatal: it is the courage to continue that counts.",
                "<PERSON> Churchill",
                "success, courage"
            )
        ]
    
    def test_analyze_quotes(self):
        """Test quote analysis functionality."""
        success = self.generator.analyze_quotes(self.test_quotes)
        self.assertTrue(success)
        
        # Check that analysis created data structures
        self.assertGreater(len(self.generator.word_chains), 0)
        self.assertGreater(len(self.generator.sentence_starters), 0)
    
    def test_generate_quote(self):
        """Test basic quote generation."""
        self.generator.analyze_quotes(self.test_quotes)
        
        # Generate a quote
        quote = self.generator.generate_quote()
        
        # Basic validation
        self.assertIsInstance(quote, str)
        self.assertGreater(len(quote), 5)
        self.assertTrue(quote.endswith(('.', '!', '?')))
    
    def test_generate_category_quote(self):
        """Test category-specific quote generation."""
        self.generator.analyze_quotes(self.test_quotes)
        
        # Generate quotes for different categories
        love_quote = self.generator.generate_category_quote("love")
        wisdom_quote = self.generator.generate_category_quote("wisdom")
        
        # Basic validation
        self.assertIsInstance(love_quote, str)
        self.assertIsInstance(wisdom_quote, str)
        self.assertGreater(len(love_quote), 5)
        self.assertGreater(len(wisdom_quote), 5)
    
    def test_generate_author_style_quote(self):
        """Test author style quote generation."""
        self.generator.analyze_quotes(self.test_quotes)
        
        # Generate quotes in author styles
        jobs_quote = self.generator.generate_author_style_quote("Steve Jobs")
        churchill_quote = self.generator.generate_author_style_quote("Winston Churchill")
        
        # Basic validation
        self.assertIsInstance(jobs_quote, str)
        self.assertIsInstance(churchill_quote, str)
        self.assertGreater(len(jobs_quote), 5)
        self.assertGreater(len(churchill_quote), 5)
    
    def test_get_generation_statistics(self):
        """Test statistics generation."""
        self.generator.analyze_quotes(self.test_quotes)
        
        stats = self.generator.get_generation_statistics()
        
        # Check required fields
        self.assertIn('word_chains', stats)
        self.assertIn('category_templates', stats)
        self.assertIn('author_vocabularies', stats)
        self.assertIn('cache_size', stats)
        
        # Check values
        self.assertGreater(stats['word_chains'], 0)
        self.assertIsInstance(stats['cache_size'], int)
    
    def test_text_cleaning(self):
        """Test text cleaning functionality."""
        # Test quote with surrounding quotes
        cleaned = self.generator._clean_text('"This is a test quote"')
        self.assertEqual(cleaned, "This is a test quote.")
        
        # Test quote without ending punctuation
        cleaned = self.generator._clean_text("This is a test")
        self.assertEqual(cleaned, "This is a test.")
        
        # Test quote with extra whitespace
        cleaned = self.generator._clean_text("  This   is   a   test  ")
        self.assertEqual(cleaned, "This is a test.")


@unittest.skipUnless(AI_MANAGER_AVAILABLE, "AI Manager not available")
class TestAIQuoteManager(unittest.TestCase):
    """Test cases for AIQuoteManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create temporary directory for models
        self.temp_dir = tempfile.mkdtemp()
        self.ai_manager = AIQuoteManager(model_dir=self.temp_dir, use_full_ai=False)  # Use simple AI for testing
        
        # Create test quotes
        self.test_quotes = [
            create_quote_from_csv_row(
                "Life is beautiful and full of surprises.",
                "Test Author",
                "life, beauty"
            ),
            create_quote_from_csv_row(
                "Love conquers all obstacles in its path.",
                "Another Author",
                "love, strength"
            ),
            create_quote_from_csv_row(
                "Wisdom is the reward you get for a lifetime of listening.",
                "Wise Author",
                "wisdom, learning"
            )
        ]
    
    def tearDown(self):
        """Clean up after tests."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initialize_ai(self):
        """Test AI initialization."""
        success = self.ai_manager.initialize_ai(self.test_quotes)
        self.assertTrue(success)
        
        # Check that simple generator is ready
        self.assertIsNotNone(self.ai_manager.simple_generator)
        self.assertGreater(len(self.ai_manager.simple_generator.word_chains), 0)
    
    def test_generate_random_quote(self):
        """Test random quote generation."""
        self.ai_manager.initialize_ai(self.test_quotes)
        
        quote = self.ai_manager.generate_random_quote()
        
        self.assertIsInstance(quote, str)
        self.assertGreater(len(quote), 5)
    
    def test_generate_category_quote(self):
        """Test category quote generation."""
        self.ai_manager.initialize_ai(self.test_quotes)
        
        quotes = self.ai_manager.generate_category_quote("love", num_quotes=2)
        
        self.assertIsInstance(quotes, list)
        self.assertLessEqual(len(quotes), 2)
        
        for quote in quotes:
            self.assertIsInstance(quote, str)
            self.assertGreater(len(quote), 5)
    
    def test_generate_author_style_quote(self):
        """Test author style quote generation."""
        self.ai_manager.initialize_ai(self.test_quotes)
        
        quotes = self.ai_manager.generate_author_style_quote("Test Author", num_quotes=1)
        
        self.assertIsInstance(quotes, list)
        self.assertLessEqual(len(quotes), 1)
        
        for quote in quotes:
            self.assertIsInstance(quote, str)
            self.assertGreater(len(quote), 5)
    
    def test_get_ai_statistics(self):
        """Test AI statistics."""
        self.ai_manager.initialize_ai(self.test_quotes)
        
        stats = self.ai_manager.get_ai_statistics()
        
        # Check required fields
        self.assertIn('full_ai_available', stats)
        self.assertIn('using_full_ai', stats)
        self.assertIn('training_data_size', stats)
        self.assertIn('generator_type', stats)
        
        # Check values
        self.assertEqual(stats['training_data_size'], len(self.test_quotes))
        self.assertIn(stats['generator_type'], ['full_ai', 'simple'])
    
    def test_get_generation_capabilities(self):
        """Test generation capabilities."""
        self.ai_manager.initialize_ai(self.test_quotes)
        
        capabilities = self.ai_manager.get_generation_capabilities()
        
        # Check required fields
        self.assertIn('random_generation', capabilities)
        self.assertIn('category_generation', capabilities)
        self.assertIn('author_style_generation', capabilities)
        self.assertIn('can_train', capabilities)
        
        # Basic capabilities should be available
        self.assertTrue(capabilities['random_generation'])
        self.assertTrue(capabilities['category_generation'])
        self.assertTrue(capabilities['author_style_generation'])
    
    def test_is_ai_ready(self):
        """Test AI readiness check."""
        # Before initialization
        self.assertFalse(self.ai_manager.is_ai_ready())
        
        # After initialization
        self.ai_manager.initialize_ai(self.test_quotes)
        self.assertTrue(self.ai_manager.is_ai_ready())


class TestAIIntegration(unittest.TestCase):
    """Test AI integration with the main quote manager."""
    
    def setUp(self):
        """Set up test fixtures."""
        # This test only checks that imports work correctly
        pass
    
    def test_ai_imports(self):
        """Test that AI modules can be imported."""
        # Test simple generator import
        from ai.simple_generator import SimpleQuoteGenerator
        generator = SimpleQuoteGenerator()
        self.assertIsNotNone(generator)
        
        # Test AI manager import (may fail if dependencies not available)
        try:
            from ai.ai_manager import AIQuoteManager
            manager = AIQuoteManager(use_full_ai=False)
            self.assertIsNotNone(manager)
        except ImportError:
            # This is expected if AI dependencies are not installed
            pass
    
    def test_quote_manager_ai_integration(self):
        """Test that quote manager can handle AI integration."""
        from core.quote_manager import QuoteManager
        
        # Create a temporary CSV for testing
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        csv_path = temp_file.name
        
        # Write test data
        test_data = [
            ["quote", "author", "category"],
            ["Test quote one", "Test Author", "test"],
            ["Test quote two", "Another Author", "test"]
        ]
        
        writer = csv.writer(temp_file)
        for row in test_data:
            writer.writerow(row)
        temp_file.close()
        
        try:
            # Create quote manager
            quote_manager = QuoteManager(csv_path, chunk_size=100)
            quote_manager.load_quotes(use_cache=False)
            
            # Test AI availability check
            ai_available = quote_manager.is_ai_available()
            self.assertIsInstance(ai_available, bool)
            
            # Test AI capabilities
            capabilities = quote_manager.get_ai_capabilities()
            self.assertIsInstance(capabilities, dict)
            self.assertIn('ai_available', capabilities)
            
            # Test AI statistics
            ai_stats = quote_manager.get_ai_statistics()
            self.assertIsInstance(ai_stats, dict)
            
        finally:
            # Clean up
            Path(csv_path).unlink(missing_ok=True)


if __name__ == '__main__':
    unittest.main()
