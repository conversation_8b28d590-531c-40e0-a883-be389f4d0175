"""
Unit tests for the QuoteManager class.

This module contains comprehensive tests for the core quote management
functionality including loading, filtering, searching, and user interactions.
"""

import unittest
import tempfile
import csv
from pathlib import Path
import sys

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from data.models import Quote, Author, Category, create_quote_from_csv_row
from core.quote_manager import QuoteManager


class TestQuoteManager(unittest.TestCase):
    """Test cases for QuoteManager class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create a temporary CSV file with test data
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        self.csv_path = self.temp_file.name
        
        # Test data
        test_quotes = [
            ["quote", "author", "category"],
            ["Life is what happens to you while you're busy making other plans.", "<PERSON>", "life, wisdom"],
            ["The only way to do great work is to love what you do.", "<PERSON>", "work, inspiration"],
            ["In the end, we will remember not the words of our enemies, but the silence of our friends.", "<PERSON>", "friendship, wisdom"],
            ["Be yourself; everyone else is already taken.", "<PERSON>", "authenticity, humor"],
            ["Two things are infinite: the universe and human stupidity; and I'm not sure about the universe.", "Albert Einstein", "humor, wisdom"]
        ]
        
        # Write test data to CSV
        writer = csv.writer(self.temp_file)
        for row in test_quotes:
            writer.writerow(row)
        
        self.temp_file.close()
        
        # Create QuoteManager instance
        self.quote_manager = QuoteManager(self.csv_path, chunk_size=100)
    
    def tearDown(self):
        """Clean up after each test method."""
        # Remove temporary file
        Path(self.csv_path).unlink(missing_ok=True)
    
    def test_load_quotes(self):
        """Test loading quotes from CSV file."""
        count = self.quote_manager.load_quotes(use_cache=False)
        
        # Should load 5 quotes (excluding header)
        self.assertEqual(count, 5)
        self.assertEqual(len(self.quote_manager.quotes), 5)
        
        # Check first quote
        first_quote = self.quote_manager.quotes[0]
        self.assertIsInstance(first_quote, Quote)
        self.assertEqual(first_quote.author.name, "John Lennon")
        self.assertTrue(first_quote.has_category("life"))
        self.assertTrue(first_quote.has_category("wisdom"))
    
    def test_get_random_quote(self):
        """Test random quote generation."""
        self.quote_manager.load_quotes(use_cache=False)
        
        # Get random quote
        quote = self.quote_manager.get_random_quote()
        self.assertIsNotNone(quote)
        self.assertIsInstance(quote, Quote)
        
        # Quote should be from our test data
        authors = ["John Lennon", "Steve Jobs", "Martin Luther King Jr.", "Oscar Wilde", "Albert Einstein"]
        self.assertIn(quote.author.name, authors)
    
    def test_filter_by_author(self):
        """Test filtering quotes by author."""
        self.quote_manager.load_quotes(use_cache=False)
        
        # Filter by existing author
        jobs_quotes = self.quote_manager.filter_by_author("Steve Jobs")
        self.assertEqual(len(jobs_quotes), 1)
        self.assertEqual(jobs_quotes[0].author.name, "Steve Jobs")
        
        # Filter by non-existing author
        no_quotes = self.quote_manager.filter_by_author("Unknown Author")
        self.assertEqual(len(no_quotes), 0)
        
        # Case insensitive test
        jobs_quotes_lower = self.quote_manager.filter_by_author("steve jobs")
        self.assertEqual(len(jobs_quotes_lower), 1)
    
    def test_filter_by_category(self):
        """Test filtering quotes by category."""
        self.quote_manager.load_quotes(use_cache=False)
        
        # Filter by existing category
        wisdom_quotes = self.quote_manager.filter_by_category("wisdom")
        self.assertEqual(len(wisdom_quotes), 3)  # John Lennon, MLK Jr., Einstein
        
        # Filter by non-existing category
        no_quotes = self.quote_manager.filter_by_category("nonexistent")
        self.assertEqual(len(no_quotes), 0)
        
        # Case insensitive test
        humor_quotes = self.quote_manager.filter_by_category("HUMOR")
        self.assertEqual(len(humor_quotes), 2)  # Oscar Wilde, Einstein
    
    def test_search_quotes(self):
        """Test quote search functionality."""
        self.quote_manager.load_quotes(use_cache=False)
        
        # Search in quote text
        life_quotes = self.quote_manager.search_quotes("life")
        self.assertGreaterEqual(len(life_quotes), 1)
        
        # Search in author name
        einstein_quotes = self.quote_manager.search_quotes("Einstein")
        self.assertEqual(len(einstein_quotes), 1)
        self.assertEqual(einstein_quotes[0].author.name, "Albert Einstein")
        
        # Search in categories
        wisdom_quotes = self.quote_manager.search_quotes("wisdom")
        self.assertEqual(len(wisdom_quotes), 3)
        
        # Empty search
        empty_results = self.quote_manager.search_quotes("")
        self.assertEqual(len(empty_results), 0)
    
    def test_toggle_favorite(self):
        """Test toggling favorite status."""
        self.quote_manager.load_quotes(use_cache=False)
        
        # Get a quote and toggle favorite
        quote = self.quote_manager.quotes[0]
        quote_id = quote.id
        
        # Initially not favorite
        self.assertFalse(quote.is_favorite)
        
        # Toggle to favorite
        success = self.quote_manager.toggle_favorite(quote_id)
        self.assertTrue(success)
        self.assertTrue(quote.is_favorite)
        
        # Toggle back to not favorite
        success = self.quote_manager.toggle_favorite(quote_id)
        self.assertTrue(success)
        self.assertFalse(quote.is_favorite)
        
        # Test with invalid ID
        success = self.quote_manager.toggle_favorite("invalid_id")
        self.assertFalse(success)
    
    def test_rate_quote(self):
        """Test quote rating functionality."""
        self.quote_manager.load_quotes(use_cache=False)
        
        # Get a quote and rate it
        quote = self.quote_manager.quotes[0]
        quote_id = quote.id
        
        # Initially no rating
        self.assertIsNone(quote.rating)
        
        # Rate the quote
        success = self.quote_manager.rate_quote(quote_id, 4.5)
        self.assertTrue(success)
        self.assertEqual(quote.rating, 4.5)
        
        # Test invalid rating
        success = self.quote_manager.rate_quote(quote_id, 6.0)
        self.assertFalse(success)
        
        # Test with invalid ID
        success = self.quote_manager.rate_quote("invalid_id", 3.0)
        self.assertFalse(success)
    
    def test_get_statistics(self):
        """Test statistics generation."""
        self.quote_manager.load_quotes(use_cache=False)
        
        stats = self.quote_manager.get_statistics()
        
        # Check basic statistics
        self.assertEqual(stats['total_quotes'], 5)
        self.assertEqual(stats['unique_authors'], 5)
        self.assertGreater(stats['unique_categories'], 0)
        self.assertEqual(stats['favorite_count'], 0)
        self.assertEqual(stats['rated_quotes'], 0)
        
        # Check that top authors and categories are present
        self.assertIn('top_authors', stats)
        self.assertIn('top_categories', stats)
        self.assertIsInstance(stats['top_authors'], list)
        self.assertIsInstance(stats['top_categories'], list)


class TestQuoteModels(unittest.TestCase):
    """Test cases for Quote data models."""
    
    def test_create_quote_from_csv_row(self):
        """Test creating Quote from CSV row data."""
        quote = create_quote_from_csv_row(
            "Test quote text",
            "Test Author",
            "category1, category2, category3"
        )
        
        self.assertEqual(quote.text, "Test quote text")
        self.assertEqual(quote.author.name, "Test Author")
        self.assertEqual(len(quote.categories), 3)
        self.assertTrue(quote.has_category("category1"))
        self.assertTrue(quote.has_category("category2"))
        self.assertTrue(quote.has_category("category3"))
    
    def test_quote_search_matching(self):
        """Test quote search matching functionality."""
        quote = create_quote_from_csv_row(
            "Life is beautiful",
            "Unknown Author",
            "life, beauty, inspiration"
        )
        
        # Test text matching
        self.assertTrue(quote.matches_search("life"))
        self.assertTrue(quote.matches_search("beautiful"))
        self.assertTrue(quote.matches_search("Life"))  # Case insensitive
        
        # Test author matching
        self.assertTrue(quote.matches_search("unknown"))
        self.assertTrue(quote.matches_search("author"))
        
        # Test category matching
        self.assertTrue(quote.matches_search("beauty"))
        self.assertTrue(quote.matches_search("inspiration"))
        
        # Test non-matching
        self.assertFalse(quote.matches_search("nonexistent"))
    
    def test_quote_id_generation(self):
        """Test quote ID generation."""
        quote1 = create_quote_from_csv_row("Same text", "Author 1", "category")
        quote2 = create_quote_from_csv_row("Same text", "Author 2", "category")
        quote3 = create_quote_from_csv_row("Same text", "Author 1", "different")
        
        # Different authors should have different IDs
        self.assertNotEqual(quote1.id, quote2.id)
        
        # Same text and author should have same ID regardless of categories
        self.assertEqual(quote1.id, quote3.id)


if __name__ == '__main__':
    unittest.main()
