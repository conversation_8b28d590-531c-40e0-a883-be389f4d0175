"""
Output formatting utilities for the quote generator application.

This module provides functions for formatting quotes, statistics, and other
output in a clean, readable format for the command-line interface.
"""

import textwrap
from typing import List, Dict, Any
from colorama import Fore, Back, Style, init

from data.models import Quote

# Initialize colorama for cross-platform colored output
init(autoreset=True)


class QuoteFormatter:
    """
    Formatter for displaying quotes in various formats.
    """
    
    def __init__(self, width: int = 80):
        """
        Initialize quote formatter.
        
        Args:
            width: Maximum width for text wrapping
        """
        self.width = width
    
    def format_quote(self, quote: Quote, show_metadata: bool = True, 
                    show_rating: bool = True, colored: bool = True) -> str:
        """
        Format a single quote for display.
        
        Args:
            quote: Quote object to format
            show_metadata: Whether to show categories and metadata
            show_rating: Whether to show rating information
            colored: Whether to use colored output
            
        Returns:
            Formatted quote string
        """
        lines = []
        
        # Quote text with proper wrapping
        quote_text = f'"{quote.text}"'
        wrapped_text = textwrap.fill(quote_text, width=self.width - 4)
        
        if colored:
            wrapped_text = f"{Fore.CYAN}{wrapped_text}{Style.RESET_ALL}"
        
        lines.append(wrapped_text)
        
        # Author
        author_line = f"— {quote.author.name}"
        if colored:
            author_line = f"{Fore.YELLOW}{author_line}{Style.RESET_ALL}"
        lines.append(author_line)
        
        if show_metadata:
            # Categories
            if quote.categories:
                categories_str = ", ".join(sorted(cat.name for cat in quote.categories))
                categories_line = f"Categories: {categories_str}"
                if colored:
                    categories_line = f"{Fore.GREEN}Categories: {Style.RESET_ALL}{categories_str}"
                lines.append(categories_line)
            
            # Rating and favorites
            metadata_parts = []
            
            if show_rating and quote.rating is not None:
                stars = "★" * int(quote.rating) + "☆" * (5 - int(quote.rating))
                rating_str = f"Rating: {stars} ({quote.rating:.1f}/5)"
                if colored:
                    rating_str = f"{Fore.MAGENTA}Rating: {Style.RESET_ALL}{stars} ({quote.rating:.1f}/5)"
                metadata_parts.append(rating_str)
            
            if quote.is_favorite:
                fav_str = "❤ Favorite"
                if colored:
                    fav_str = f"{Fore.RED}❤ Favorite{Style.RESET_ALL}"
                metadata_parts.append(fav_str)
            
            if quote.view_count > 0:
                view_str = f"Views: {quote.view_count}"
                if colored:
                    view_str = f"{Fore.BLUE}Views: {Style.RESET_ALL}{quote.view_count}"
                metadata_parts.append(view_str)
            
            if metadata_parts:
                lines.append(" | ".join(metadata_parts))
        
        # Quote ID (for reference)
        if show_metadata:
            id_line = f"ID: {quote.id}"
            if colored:
                id_line = f"{Style.DIM}ID: {quote.id}{Style.RESET_ALL}"
            lines.append(id_line)
        
        return "\n".join(lines)
    
    def format_quote_list(self, quotes: List[Quote], numbered: bool = True,
                         show_metadata: bool = False, colored: bool = True) -> str:
        """
        Format a list of quotes for display.
        
        Args:
            quotes: List of Quote objects
            numbered: Whether to number the quotes
            show_metadata: Whether to show metadata for each quote
            colored: Whether to use colored output
            
        Returns:
            Formatted quotes string
        """
        if not quotes:
            return "No quotes found."
        
        lines = []
        separator = "─" * self.width
        
        for i, quote in enumerate(quotes, 1):
            if numbered:
                number = f"[{i}] "
                if colored:
                    number = f"{Fore.WHITE}{Back.BLUE}[{i}]{Style.RESET_ALL} "
                lines.append(number)
            
            formatted_quote = self.format_quote(quote, show_metadata, colored=colored)
            lines.append(formatted_quote)
            
            # Add separator between quotes (except for the last one)
            if i < len(quotes):
                if colored:
                    lines.append(f"{Style.DIM}{separator}{Style.RESET_ALL}")
                else:
                    lines.append(separator)
        
        return "\n".join(lines)
    
    def format_compact_quote(self, quote: Quote, colored: bool = True) -> str:
        """
        Format a quote in compact form (single line).
        
        Args:
            quote: Quote object to format
            colored: Whether to use colored output
            
        Returns:
            Compact formatted quote string
        """
        # Truncate long quotes
        text = quote.text
        if len(text) > 60:
            text = text[:57] + "..."
        
        compact = f'"{text}" — {quote.author.name}'
        
        if colored:
            compact = f'{Fore.CYAN}"{text}"{Style.RESET_ALL} — {Fore.YELLOW}{quote.author.name}{Style.RESET_ALL}'
        
        # Add rating if available
        if quote.rating is not None:
            stars = "★" * int(quote.rating)
            compact += f" ({stars})"
        
        # Add favorite indicator
        if quote.is_favorite:
            fav_indicator = " ❤"
            if colored:
                fav_indicator = f" {Fore.RED}❤{Style.RESET_ALL}"
            compact += fav_indicator
        
        return compact


class StatisticsFormatter:
    """
    Formatter for displaying statistics and reports.
    """
    
    def __init__(self, colored: bool = True):
        """
        Initialize statistics formatter.
        
        Args:
            colored: Whether to use colored output
        """
        self.colored = colored
    
    def format_statistics(self, stats: Dict[str, Any]) -> str:
        """
        Format statistics dictionary for display.
        
        Args:
            stats: Statistics dictionary
            
        Returns:
            Formatted statistics string
        """
        lines = []
        
        # Header
        header = "📊 Quote Collection Statistics"
        if self.colored:
            header = f"{Fore.CYAN}{Style.BRIGHT}{header}{Style.RESET_ALL}"
        lines.append(header)
        lines.append("=" * 40)
        
        # Basic statistics
        basic_stats = [
            ("Total Quotes", stats.get('total_quotes', 0)),
            ("Unique Authors", stats.get('unique_authors', 0)),
            ("Unique Categories", stats.get('unique_categories', 0)),
            ("Favorite Quotes", stats.get('favorite_count', 0)),
            ("Rated Quotes", stats.get('rated_quotes', 0)),
            ("Average Rating", f"{stats.get('average_rating', 0):.1f}/5.0"),
            ("Total Views", stats.get('total_views', 0))
        ]
        
        for label, value in basic_stats:
            line = f"{label:.<20} {value}"
            if self.colored:
                line = f"{Fore.GREEN}{label}{Style.RESET_ALL}{'.' * (20 - len(label))} {Fore.YELLOW}{value}{Style.RESET_ALL}"
            lines.append(line)
        
        # Top authors
        if 'top_authors' in stats and stats['top_authors']:
            lines.append("")
            header = "🏆 Top Authors"
            if self.colored:
                header = f"{Fore.MAGENTA}{Style.BRIGHT}{header}{Style.RESET_ALL}"
            lines.append(header)
            lines.append("-" * 20)
            
            for i, (author, count) in enumerate(stats['top_authors'][:5], 1):
                line = f"{i}. {author} ({count} quotes)"
                if self.colored:
                    line = f"{Fore.WHITE}{i}.{Style.RESET_ALL} {Fore.YELLOW}{author}{Style.RESET_ALL} ({count} quotes)"
                lines.append(line)
        
        # Top categories
        if 'top_categories' in stats and stats['top_categories']:
            lines.append("")
            header = "🏷️  Top Categories"
            if self.colored:
                header = f"{Fore.BLUE}{Style.BRIGHT}{header}{Style.RESET_ALL}"
            lines.append(header)
            lines.append("-" * 20)
            
            for i, (category, count) in enumerate(stats['top_categories'][:10], 1):
                line = f"{i:2d}. {category} ({count})"
                if self.colored:
                    line = f"{Fore.WHITE}{i:2d}.{Style.RESET_ALL} {Fore.GREEN}{category}{Style.RESET_ALL} ({count})"
                lines.append(line)
        
        return "\n".join(lines)
    
    def format_search_results_header(self, query: str, count: int) -> str:
        """
        Format search results header.
        
        Args:
            query: Search query
            count: Number of results
            
        Returns:
            Formatted header string
        """
        header = f"🔍 Search Results for '{query}' ({count} found)"
        if self.colored:
            header = f"{Fore.CYAN}🔍 Search Results for {Style.RESET_ALL}'{Fore.YELLOW}{query}{Style.RESET_ALL}' ({Fore.GREEN}{count}{Style.RESET_ALL} found)"
        
        return header
    
    def format_filter_header(self, filter_type: str, filter_value: str, count: int) -> str:
        """
        Format filter results header.
        
        Args:
            filter_type: Type of filter (author, category, rating)
            filter_value: Filter value
            count: Number of results
            
        Returns:
            Formatted header string
        """
        header = f"🔽 {filter_type.title()} Filter: '{filter_value}' ({count} found)"
        if self.colored:
            header = f"{Fore.BLUE}🔽 {filter_type.title()} Filter:{Style.RESET_ALL} '{Fore.YELLOW}{filter_value}{Style.RESET_ALL}' ({Fore.GREEN}{count}{Style.RESET_ALL} found)"
        
        return header


def format_error(message: str, colored: bool = True) -> str:
    """
    Format error message.
    
    Args:
        message: Error message
        colored: Whether to use colored output
        
    Returns:
        Formatted error message
    """
    error_msg = f"❌ Error: {message}"
    if colored:
        error_msg = f"{Fore.RED}❌ Error: {message}{Style.RESET_ALL}"
    return error_msg


def format_success(message: str, colored: bool = True) -> str:
    """
    Format success message.
    
    Args:
        message: Success message
        colored: Whether to use colored output
        
    Returns:
        Formatted success message
    """
    success_msg = f"✅ {message}"
    if colored:
        success_msg = f"{Fore.GREEN}✅ {message}{Style.RESET_ALL}"
    return success_msg


def format_info(message: str, colored: bool = True) -> str:
    """
    Format info message.
    
    Args:
        message: Info message
        colored: Whether to use colored output
        
    Returns:
        Formatted info message
    """
    info_msg = f"ℹ️  {message}"
    if colored:
        info_msg = f"{Fore.BLUE}ℹ️  {message}{Style.RESET_ALL}"
    return info_msg
