"""
Performance optimization utilities for the quote generator application.

This module provides decorators and utilities for monitoring and optimizing
performance, including timing, memory usage, and caching helpers.
"""

import time
import functools
import logging
import psutil
import os
from typing import Callable, Any, Dict
from collections import OrderedDict


class LRUCache:
    """
    Simple LRU (Least Recently Used) cache implementation.
    
    More memory-efficient than functools.lru_cache for our specific use case.
    """
    
    def __init__(self, max_size: int = 128):
        """
        Initialize LRU cache.
        
        Args:
            max_size: Maximum number of items to cache
        """
        self.max_size = max_size
        self.cache: OrderedDict = OrderedDict()
    
    def get(self, key: str) -> Any:
        """
        Get item from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        if key in self.cache:
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            return self.cache[key]
        return None
    
    def put(self, key: str, value: Any):
        """
        Put item in cache.
        
        Args:
            key: Cache key
            value: Value to cache
        """
        if key in self.cache:
            # Update existing item
            self.cache.move_to_end(key)
        else:
            # Add new item
            if len(self.cache) >= self.max_size:
                # Remove least recently used item
                self.cache.popitem(last=False)
        
        self.cache[key] = value
    
    def clear(self):
        """Clear all cached items."""
        self.cache.clear()
    
    def size(self) -> int:
        """Get current cache size."""
        return len(self.cache)


def timer(func: Callable) -> Callable:
    """
    Decorator to measure function execution time.
    
    Args:
        func: Function to time
        
    Returns:
        Wrapped function with timing
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        logger = logging.getLogger(func.__module__)
        logger.debug(f"{func.__name__} executed in {execution_time:.4f} seconds")
        
        return result
    return wrapper


def memory_monitor(func: Callable) -> Callable:
    """
    Decorator to monitor memory usage of a function.
    
    Args:
        func: Function to monitor
        
    Returns:
        Wrapped function with memory monitoring
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        process = psutil.Process(os.getpid())
        
        # Memory before execution
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        result = func(*args, **kwargs)
        
        # Memory after execution
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_diff = memory_after - memory_before
        
        logger = logging.getLogger(func.__module__)
        logger.debug(f"{func.__name__} memory usage: {memory_diff:+.2f} MB "
                    f"(total: {memory_after:.2f} MB)")
        
        return result
    return wrapper


def cached_property(func: Callable) -> property:
    """
    Decorator for cached properties that are computed once and then cached.
    
    Args:
        func: Property function to cache
        
    Returns:
        Cached property
    """
    attr_name = f"_cached_{func.__name__}"
    
    @functools.wraps(func)
    def wrapper(self):
        if not hasattr(self, attr_name):
            setattr(self, attr_name, func(self))
        return getattr(self, attr_name)
    
    return property(wrapper)


class PerformanceMonitor:
    """
    Performance monitoring utility for tracking application metrics.
    """
    
    def __init__(self):
        """Initialize performance monitor."""
        self.metrics: Dict[str, list] = {}
        self.logger = logging.getLogger(__name__)
    
    def record_timing(self, operation: str, duration: float):
        """
        Record timing for an operation.
        
        Args:
            operation: Name of the operation
            duration: Duration in seconds
        """
        if operation not in self.metrics:
            self.metrics[operation] = []
        
        self.metrics[operation].append(duration)
        
        # Keep only last 100 measurements
        if len(self.metrics[operation]) > 100:
            self.metrics[operation] = self.metrics[operation][-50:]
    
    def get_average_time(self, operation: str) -> float:
        """
        Get average execution time for an operation.
        
        Args:
            operation: Name of the operation
            
        Returns:
            Average execution time in seconds
        """
        if operation not in self.metrics or not self.metrics[operation]:
            return 0.0
        
        return sum(self.metrics[operation]) / len(self.metrics[operation])
    
    def get_performance_report(self) -> Dict[str, Dict[str, float]]:
        """
        Get comprehensive performance report.
        
        Returns:
            Dictionary with performance statistics
        """
        report = {}
        
        for operation, timings in self.metrics.items():
            if timings:
                report[operation] = {
                    'count': len(timings),
                    'average': sum(timings) / len(timings),
                    'min': min(timings),
                    'max': max(timings),
                    'total': sum(timings)
                }
        
        return report
    
    def clear_metrics(self):
        """Clear all recorded metrics."""
        self.metrics.clear()


def get_memory_usage() -> Dict[str, float]:
    """
    Get current memory usage statistics.
    
    Returns:
        Dictionary with memory usage in MB
    """
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    
    return {
        'rss': memory_info.rss / 1024 / 1024,  # Resident Set Size
        'vms': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
        'percent': process.memory_percent(),    # Percentage of total memory
        'available': psutil.virtual_memory().available / 1024 / 1024
    }


def optimize_for_memory():
    """
    Apply memory optimizations for the application.
    
    This function can be called to enable various memory optimizations.
    """
    import gc
    
    # Force garbage collection
    gc.collect()
    
    # Set garbage collection thresholds for better memory management
    gc.set_threshold(700, 10, 10)


class BatchProcessor:
    """
    Utility for processing large datasets in batches to optimize memory usage.
    """
    
    def __init__(self, batch_size: int = 1000):
        """
        Initialize batch processor.
        
        Args:
            batch_size: Number of items to process in each batch
        """
        self.batch_size = batch_size
        self.logger = logging.getLogger(__name__)
    
    def process_in_batches(self, items: list, processor_func: Callable, 
                          progress_callback: Callable = None) -> list:
        """
        Process items in batches.
        
        Args:
            items: List of items to process
            processor_func: Function to apply to each batch
            progress_callback: Optional callback for progress updates
            
        Returns:
            List of processed results
        """
        results = []
        total_batches = (len(items) + self.batch_size - 1) // self.batch_size
        
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            batch_results = processor_func(batch)
            results.extend(batch_results)
            
            # Call progress callback if provided
            if progress_callback:
                batch_num = (i // self.batch_size) + 1
                progress_callback(batch_num, total_batches)
        
        return results


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
